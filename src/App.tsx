import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter,
  Routes,
  Route,
  useLocation,
  Outlet,
} from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { AuthProvider } from "@/contexts/AuthContext";
import { WorkflowProvider } from "@/contexts/WorkflowContext";
import { AgentProvider } from "@/contexts/AgentContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Dashboard from "./pages/Dashboard";
import ToolsManagement from "./pages/ToolsManagement";
import ModelsManagement from "./pages/ModelsManagement";
import TerminationsManagement from "./pages/TerminationsManagement";
import Agents2Management from "./pages/Agents2Management";
import APIKeysManagement from "./pages/APIKeysManagement";
import CreateTeam from "./pages/CreateTeam";
import EditTeam from "./pages/EditTeam";

import Settings from "./pages/Settings";
import Login from "./pages/Login";
import Register from "./pages/Register";
import NotFound from "./pages/NotFound";

import DeployedAppLayout from "@/components/app/pages/home.tsx";
import DeployedAppInterface from "@/components/app/pages/interface.tsx";

// Import the new OAuthCallback component
import { OAuthCallback } from "@/components/auth/OAuthCallback";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // default: true
      // refetchInterval: 6000, // refetch every minute instead
    },
  },
});

// Sidebar layout component
const SidebarLayout = () => (
  <div className="min-h-screen flex w-full bg-gray-50">
    <AppSidebar />
    <main className="flex-1">
      <Outlet />
    </main>
  </div>
);

const AppContent = () => {
  const location = useLocation();
  const isAuthRoute =
    location.pathname === "/login" || location.pathname === "/register" || location.pathname === "/google/auth/callback" || location.pathname === "/github/auth/callback";
  const isCreateRoute = location.pathname === "/create";
  const isEditRoute =
    location.pathname.startsWith("/agent/edit") ||
    location.pathname.startsWith("/team/edit");
  const isChatRoute = location.pathname.startsWith("/workflows");

  // Public routes (auth pages)
  if (isAuthRoute) {
    return (
      <main className="w-full">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/google/auth/callback" element={<OAuthCallback />} />
          <Route path="/github/auth/callback" element={<OAuthCallback />} />
        </Routes>
      </main>
    );
  }

  // Protected full-screen routes (create, edit, chat)
  if (isCreateRoute || isEditRoute) {
    return (
      <ProtectedRoute>
        <WorkflowProvider>
          <AgentProvider>
            <main className="w-full">
              <Routes>
                <Route path="/create" element={<CreateTeam />} />
                <Route path="/agent/edit/:id" element={<EditTeam />} />
                <Route path="/team/edit/:id" element={<EditTeam />} />
              </Routes>
            </main>
          </AgentProvider>
        </WorkflowProvider>
      </ProtectedRoute>
    );
  }

  if (isChatRoute) {
    return (
      <ProtectedRoute>
        <Routes>
          <Route path="/workflows/:teamId/chat/sessions" element={<DeployedAppLayout />}>
            <Route path=":conversationId" element={<DeployedAppInterface />} />
          </Route>
        </Routes>
      </ProtectedRoute>
    );
  }

  // Protected sidebar layout routes
  return (
    <ProtectedRoute>
      <WorkflowProvider>
        <AgentProvider>
          <SidebarProvider>
            <Routes>
              <Route path="/" element={<SidebarLayout />}>
                <Route index element={<Dashboard />} />
                <Route path="agents" element={<Agents2Management />} />
                <Route path="tools" element={<ToolsManagement />} />
                <Route path="models" element={<ModelsManagement />} />
                <Route
                  path="terminations"
                  element={<TerminationsManagement />}
                />
                <Route path="api-keys" element={<APIKeysManagement />} />
                <Route path="settings" element={<Settings />} />
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </SidebarProvider>
        </AgentProvider>
      </WorkflowProvider>
    </ProtectedRoute>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AppContent />
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
