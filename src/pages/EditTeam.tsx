import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowRight,
  Check,
  User,
  Users,
  Setting<PERSON>,
  Rocket,
  ArrowUpDown,
  Loader2,
  <PERSON><PERSON><PERSON><PERSON>,
  Wrench,
  Play,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Link, useParams, useNavigate, useLocation } from "react-router-dom";
import {
  useTeam,
  useUpdateTeam,
  useDeployTeam,
  useUndeployTeam,
  useTestTeam,
} from "@/hooks/useTeams";
import { useInputsByIds } from "@/hooks/useInputs";
import { TeamResponse, TeamUpdate, TeamComponent } from "@/types/team";
import { useToast } from "@/hooks/use-toast";
import { transformAgentsToParticipants } from "@/lib/teamUtils";

// Import all step components
import { teamDetailsStep } from "@/components/steps/TeamDetailsStep";
import { SubAgentsStep } from "@/components/steps/SubAgentsStep";
import { IOStep } from "@/components/steps/IOStep";
import { TeamStep } from "@/components/steps/TeamStep";
import { DeployStep } from "@/components/steps/DeployStep";

const steps = [
  { id: 1, title: "Workflow Details", component: teamDetailsStep, icon: User },
  { id: 2, title: "Sub Agents", component: SubAgentsStep, icon: Users },
  { id: 3, title: "IO", component: IOStep, icon: ArrowUpDown },
  { id: 4, title: "Configuration", component: TeamStep, icon: Settings },
  { id: 5, title: "Deploy", component: DeployStep, icon: Rocket },
];

// Mock data for existing agent (fallback)
const mockAgentData = {
  name: "Customer Support Agent",
  description:
    "Handles customer inquiries and support tickets with intelligent routing and escalation",
  model: "GPT-4",
  status: "Active",
  tools: ["task-actions", "media"],
  systemPrompt: "You are a helpful customer support agent...",
};

// Transform workflow state back to TeamUpdate format for API
const transformWorkflowStateToTeamUpdate = async (
  workflowState: any,
  originalTeam: TeamResponse,
): Promise<TeamUpdate> => {
  const config = { ...originalTeam.component.config };

  // Update basic team configuration
  if (workflowState.teamConfig) {
    Object.assign(config, workflowState.teamConfig);
  }

  // Use actual selected agents instead of customAgents from template
  if (workflowState.selectedAgents && workflowState.selectedAgents.length > 0) {
    // Use cached participants data if available, otherwise fetch from API
    const participants =
      workflowState.participantsData ||
      (await transformAgentsToParticipants(workflowState.selectedAgents));
    config.participants = participants;
  }

  // Create the updated component
  const updatedComponent: TeamComponent = {
    ...originalTeam.component,
    label:
      workflowState.label || workflowState.name || originalTeam.component.label,
    description:
      workflowState.description || originalTeam.component.description,
    config: config,
  };

  // Extract termination condition ID from multiple possible sources
  // Prioritize teamConfig.termination_condition since that's what gets updated by the UI
  let terminationConditionIds = [];
  if (workflowState.teamConfig?.termination_condition) {
    // Handle single termination condition ID stored in teamConfig (this is the primary source)
    terminationConditionIds = Array.isArray(
      workflowState.teamConfig.termination_condition,
    )
      ? workflowState.teamConfig.termination_condition.filter(Boolean)
      : [workflowState.teamConfig.termination_condition];
    console.log(
      "Using termination condition from teamConfig:",
      terminationConditionIds,
    );
  } else if (workflowState.selectedTerminationIds) {
    terminationConditionIds = workflowState.selectedTerminationIds;
    console.log(
      "Using termination condition from selectedTerminationIds:",
      terminationConditionIds,
    );
  } else {
    terminationConditionIds =
      (originalTeam as any).team_termination_condition_ids || [];
    console.log(
      "Using termination condition from originalTeam:",
      terminationConditionIds,
    );
  }

  return {
    component: updatedComponent,
    organization_id: originalTeam.organization_id,
    // Include related IDs if they exist in the workflow state
    model_id: workflowState.selectedModelId || (originalTeam as any).model_id,
    team_agent_ids:
      workflowState.selectedAgents ||
      (originalTeam as any).team_agent_ids ||
      [],
    team_input_ids:
      workflowState.team_input_ids ||
      (originalTeam as any).team_input_ids ||
      [],
    team_output_ids:
      workflowState.team_output_ids ||
      (originalTeam as any).team_output_ids ||
      [],
    team_termination_condition_ids: terminationConditionIds,
  };
};

// Transform TeamResponse to workflow state format with actual input component details
const transformTeamToWorkflowStateWithInputs = (
  teamResponse: TeamResponse,
  inputsData: any[] = [],
) => {
  const component = teamResponse.component;
  const config = component.config;

  // Extract team type from provider (e.g., "autogen_agentchat.teams.SelectorGroupChat" -> "SelectorGroupChat")
  const teamType = component.provider?.split(".").pop() || "";

  // Extract model client information for SelectorGroupChat
  let selectedModelId = "";
  let selectedModel = null;

  // First check if there's a model_id at the team level (this is the ID we want)
  if ((teamResponse as any).model_id) {
    selectedModelId = (teamResponse as any).model_id;
  } else if (config.model_client) {
    // Fallback: try to extract from model_client config, but this might be the model name, not ID
    selectedModelId = config.model_client.config?.model || "";
  }

  if (selectedModelId) {
    selectedModel = {
      modelId: selectedModelId,
      // Add other model properties as needed
    };
  }

  // Create configurableInputs with actual input component details
  const teamInputIds = (teamResponse as any).team_input_ids || [];
  const configurableInputs =
    teamInputIds.length > 0 && inputsData.length > 0
      ? teamInputIds.map((inputId: string, index: number) => {
          const inputDetail = inputsData.find((input) => input.id === inputId);
          if (inputDetail) {
            // Extract the config type from the provider field to match builder service labels
            // e.g., "autogen_core.io.FileInput" -> "FileInput"
            let originalConfigType = "";
            if (inputDetail.component?.provider) {
              const providerParts = inputDetail.component.provider.split(".");
              originalConfigType = providerParts[providerParts.length - 1]; // Get the last part
            }

            // Fallback to component label if provider parsing fails
            if (!originalConfigType) {
              originalConfigType =
                inputDetail.component?.label || `Input Component ${index + 1}`;
            }

            return {
              id: `existing-input-${index + 1}`,
              configType: originalConfigType,
              name: inputDetail.component?.label || `Input ${index + 1}`,
              description: inputDetail.component?.description || "",
              config: inputDetail.component?.config || {},
              savedInputId: inputId,
              isConfigured: true,
              isModified: false,
            };
          }
          // Fallback if input detail not found
          return {
            id: `existing-input-${index + 1}`,
            configType: "Unknown",
            name: `Input ${index + 1}`,
            description: "Input component details could not be loaded",
            config: {},
            savedInputId: inputId,
            isConfigured: true,
            isModified: false,
          };
        })
      : [
          {
            id: "1",
            configType: "",
            name: "",
            description: "",
            config: {},
            isConfigured: false,
            isModified: false,
          },
        ]; // Default empty configurable input

  return {
    // Step 1: Workflow Details
    name: component.label, // For backward compatibility
    label: component.label, // User-facing display name
    description: component.description,
    teamType: teamType,

    // Step 2: Sub Agents
    selectedAgents: (teamResponse as any).team_agent_ids || [],
    customAgents: config.participants || [],

    // Step 3: IO Configuration
    selectedInputIds: teamInputIds,
    selectedOutputIds: (teamResponse as any).team_output_ids || [],
    team_input_ids: teamInputIds,
    team_output_ids: (teamResponse as any).team_output_ids || [],
    configurableInputs: configurableInputs,
    // Single output selection for IOStep
    selectedOutputId: ((teamResponse as any).team_output_ids || [])[0] || null,

    // Step 4: Team Configuration
    selectedTeamType: teamType,
    teamConfig: {
      ...config,
      // Ensure all required fields are present
      max_turns: config.max_turns || 10,
      emit_team_events: config.emit_team_events || false,
      participants: config.participants || [],
      // SelectorGroupChat specific fields
      ...(teamType === "SelectorGroupChat" && {
        selector_prompt:
          config.selector_prompt || "You are in a role play game...",
        allow_repeated_speaker: config.allow_repeated_speaker || false,
        max_selector_attempts: config.max_selector_attempts || 3,
        model_client_streaming: config.model_client_streaming || false,
        model_client: config.model_client,
      }),
      // Add termination condition ID if present
      ...((teamResponse as any).team_termination_condition_ids?.length > 0 && {
        termination_condition: (teamResponse as any)
          .team_termination_condition_ids[0],
      }),
    },
    selectedTeamTemplate: {
      provider: component.provider,
      component_type: component.component_type,
      version: component.version,
      component_version: component.component_version,
      description: component.description,
      label: teamType,
      config: config,
    },

    // Model selection for SelectorGroupChat
    ...(selectedModelId && {
      selectedModelId,
      selectedModel,
    }),

    // Step 5: Deploy
    teamId: teamResponse.id,
    deploymentComplete: false,

    // Additional metadata
    organizationId: teamResponse.organization_id,
    createdAt: teamResponse.created_at,
    updatedAt: teamResponse.updated_at,
    isDeleted: teamResponse.is_deleted,

    // Include termination conditions if present
    ...((teamResponse as any).team_termination_condition_ids && {
      selectedTerminationIds: (teamResponse as any)
        .team_termination_condition_ids,
    }),
  };
};

const EditTeam = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [currentStep, setCurrentStep] = useState(1);
  const [maxStepReached, setMaxStepReached] = useState(5); // In edit mode, all steps are accessible
  const [isDeployed, setIsDeployed] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Fetch complete team data from backend
  const { data: teamResponse, isLoading, error } = useTeam(id!);

  // Extract input IDs from team data to fetch input details
  const inputIds = teamResponse
    ? (teamResponse as any).team_input_ids || []
    : [];
  const { data: inputsData, isLoading: inputsLoading } =
    useInputsByIds(inputIds);

  // Update team mutation
  const updateTeamMutation = useUpdateTeam();

  // Deploy mutations
  const deployTeamMutation = useDeployTeam();
  const undeployTeamMutation = useUndeployTeam();
  const testTeamMutation = useTestTeam();

  // Toast for notifications
  const { toast } = useToast();

  // Get team data from router state as fallback for display name
  const routerTeamData = location.state?.teamData;

  // Validation functions for each step (simplified for edit mode)
  const validateStep1 = (data: any): boolean => {
    return !!((data?.label?.trim() || data?.name?.trim()) && data?.teamType);
  };

  const validateStep2 = (data: any): boolean => {
    return !!(data?.selectedAgents?.length > 0);
  };

  const validateStep3 = (data: any): boolean => {
    return !!(data?.configurableInputs?.length > 0 && data?.selectedOutputId);
  };

  const validateStep4 = (data: any): boolean => {
    // Team step validation
    if (!data?.teamConfig) return false;

    // A termination condition is always required.
    const hasTerminationCondition = !!data.teamConfig.termination_condition;

    // If it's a SelectorGroupChat, require model selection as well
    if (
      data?.selectedTeamTemplate?.provider ===
        "autogen_agentchat.teams.SelectorGroupChat" ||
      data?.teamType === "SelectorGroupChat"
    ) {
      const hasModel = !!(
        data?.selectedModelId || data?.teamConfig?.model_client
      );
      return hasTerminationCondition && hasModel;
    }

    // For other team types, just the termination condition is needed for this step's validation.
    return hasTerminationCondition;
  };

  const validateStep5 = (): boolean => {
    return true; // Deploy step is always accessible in edit mode
  };

  // Check if a step is completed (all required fields filled)
  const isStepCompleted = (stepId: number): boolean => {
    switch (stepId) {
      case 1:
        return validateStep1(agentData);
      case 2:
        return validateStep2(agentData);
      case 3:
        return validateStep3(agentData);
      case 4:
        return validateStep4(agentData);
      case 5:
        return validateStep5();
      default:
        return false;
    }
  };

  // Check if user can proceed to next step
  const canProceedToNextStep = (): boolean => {
    return isStepCompleted(currentStep);
  };

  // Transform backend data to workflow state format
  const [agentData, setAgentData] = useState<any>({});

  // Deploy step actions similar to CreateTeam.tsx
  const handleDeploy = useCallback(async () => {
    if (!id) return;

    try {
      await deployTeamMutation.mutateAsync(id);
      setIsDeployed(true);

      // Update agent data to indicate deployment is complete
      setAgentData((prev: any) => ({
        ...prev,
        isDeployed: true,
        deploymentComplete: true,
      }));

      toast({
        title: "Team Deployed",
        description: "Your team has been successfully deployed.",
      });
    } catch (error) {
      console.error("Failed to deploy team:", error);
      toast({
        title: "Deployment Failed",
        description:
          "There was an error deploying your team. Please try again.",
        variant: "destructive",
      });
    }
  }, [id, deployTeamMutation, toast]);

  const handleUndeploy = useCallback(async () => {
    if (!id) return;

    try {
      await undeployTeamMutation.mutateAsync(id);
      setIsDeployed(false);

      setAgentData((prev: any) => ({
        ...prev,
        isDeployed: false,
        deploymentComplete: false,
      }));

      toast({
        title: "Team Undeployed",
        description: "Your team has been undeployed.",
      });
    } catch (error) {
      console.error("Failed to undeploy team:", error);
      toast({
        title: "Undeploy Failed",
        description:
          "There was an error undeploying your team. Please try again.",
        variant: "destructive",
      });
    }
  }, [id, undeployTeamMutation, toast]);

  const deployStepActions = {
    handleDeploy,
    handleUndeploy,
    // State getters
    isCreating: false, // Not applicable in edit mode
    isCreated: !!id, // Team already exists
    isDeploying: deployTeamMutation.isPending,
    isDeployed: isDeployed,
    isUndeploying: undeployTeamMutation.isPending,
    isTesting: testTeamMutation.isPending,
    deploymentStatus: isDeployed ? "deployed" : "idle",
  };

  useEffect(() => {
    if (teamResponse) {
      console.log("Full team data from backend:", teamResponse);
      const transformedData = transformTeamToWorkflowStateWithInputs(
        teamResponse,
        inputsData,
      );
      console.log("Transformed workflow data:", transformedData);
      console.log(
        "Configurable inputs structure:",
        transformedData.configurableInputs,
      );
      console.log("Selected output ID:", transformedData.selectedOutputId);
      console.log("Selected agents:", transformedData.selectedAgents);

      // Set deployment status based on team data
      const isTeamDeployed = (teamResponse as any).is_deployed || false;
      setIsDeployed(isTeamDeployed);

      // Add deployStepActions to the transformed data
      setAgentData({
        ...transformedData,
        isDeployed: isTeamDeployed,
        deployStepActions,
      });
    } else if (routerTeamData && !isLoading) {
      // Fallback to router data if backend fetch fails, but convert to expected format
      const fallbackData = {
        name: routerTeamData.name,
        description: routerTeamData.description,
        teamType: routerTeamData.teamType,
        status: routerTeamData.status,
        participantsCount: routerTeamData.participantsCount,
        maxTurns: routerTeamData.maxTurns,
        lastModified: routerTeamData.lastModified,
        teamId: routerTeamData.id,
        // Add default empty structures for fallback
        configurableInputs: [
          {
            id: "1",
            configType: "",
            name: "",
            description: "",
            config: {},
            isConfigured: false,
          },
        ],
        selectedOutputId: null,
        selectedAgents: [],
        isDeployed: false,
        deployStepActions,
      };
      console.log("Using fallback router data:", fallbackData);
      setAgentData(fallbackData);
    }
  }, [teamResponse, routerTeamData, isLoading, inputsData]);

  useEffect(() => {
    console.log("Editing team with ID:", id);
    console.log("Router team data:", routerTeamData);
    console.log("Backend team response:", teamResponse);
  }, [id, routerTeamData, teamResponse]);

  // Show loading state while fetching data
  if (isLoading || (inputIds.length > 0 && inputsLoading)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {isLoading ? "Loading team data..." : "Loading input components..."}
          </p>
        </div>
      </div>
    );
  }

  // Show error state if team not found
  if (error || (!teamResponse && !routerTeamData)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Failed to load team data</p>
          <Button onClick={() => navigate("/")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepId: number) => {
    // In edit mode, allow access to any step that has been reached before, or the next step if current step is valid
    if (
      stepId <= maxStepReached ||
      (stepId === currentStep + 1 && canProceedToNextStep())
    ) {
      setCurrentStep(stepId);
      setMaxStepReached(Math.max(maxStepReached, stepId));
    }
  };

  const updateAgentData = (newData: any, markUnsaved: boolean = true) => {
    setAgentData((prev: any) => ({
      ...prev,
      ...newData,
      deployStepActions,
    }));
    if (markUnsaved) {
      setHasUnsavedChanges(true);
    }
  };

  const handleSave = async () => {
    if (!teamResponse || !id) {
      console.error("No team data available for update");
      return;
    }

    try {
      console.log("Saving agent data:", agentData);
      console.log(
        "Current teamConfig termination_condition:",
        agentData?.teamConfig?.termination_condition,
      );

      // Transform the workflow state back to the API format
      const updateData = await transformWorkflowStateToTeamUpdate(
        agentData,
        teamResponse,
      );
      console.log("Update payload:", updateData);
      console.log(
        "Termination condition IDs being sent:",
        updateData.team_termination_condition_ids,
      );

      // Make the API call to update the team
      await updateTeamMutation.mutateAsync({
        id: id,
        data: updateData,
      });

      console.log("Team updated successfully");

      // Show success toast
      toast({
        title: "Team Updated",
        description: "Your team has been successfully updated.",
      });

      // Reset unsaved changes
      setHasUnsavedChanges(false);
    } catch (error: any) {
      console.error("Failed to update team:", error);

      // Show error toast
      toast({
        title: "Update Failed",
        description:
          error?.message ||
          "There was an error updating your team. Please try again.",
        variant: "destructive",
      });
    }
  };

  const CurrentStepComponent = steps[currentStep - 1].component;

  return (
    <div className="min-h-screen bg-gray-50 flex pb-20">
      {/* Vertical Step Sidebar - Made Sticky */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col sticky top-0 h-screen">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-3 mb-4">
            <Link to="/">
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0 hover:bg-gray-100 rounded-lg"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Edit Workflow
              </h1>
              <p className="text-xs text-gray-500">Step by step setup</p>
            </div>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="flex-1 p-4 overflow-y-auto">
          <div className="space-y-2">
            {steps.map((step) => {
              const isActive = currentStep === step.id;
              const isCompleted = isStepCompleted(step.id);
              const isAccessible =
                step.id <= maxStepReached ||
                (step.id === currentStep + 1 && canProceedToNextStep());
              const IconComponent = step.icon;

              return (
                <button
                  key={step.id}
                  onClick={() => goToStep(step.id)}
                  disabled={!isAccessible}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors ${
                    isActive
                      ? "bg-teal-800 text-white"
                      : isCompleted
                        ? "bg-green-50 text-green-700 hover:bg-green-100"
                        : isAccessible
                          ? "text-gray-600 hover:bg-gray-100"
                          : "text-gray-400 cursor-not-allowed"
                  }`}
                >
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      isCompleted
                        ? "bg-green-100 border-green-500"
                        : isActive
                          ? "bg-white border-white"
                          : isAccessible
                            ? "border-gray-300"
                            : "border-gray-200"
                    }`}
                  >
                    {isCompleted ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <IconComponent
                        className={`h-4 w-4 ${
                          isActive
                            ? "text-teal-800"
                            : isAccessible
                              ? "text-gray-400"
                              : "text-gray-300"
                        }`}
                      />
                    )}
                  </div>
                  <div className="flex-1">
                    <div
                      className={`font-medium text-sm ${
                        isActive
                          ? "text-white"
                          : isCompleted
                            ? "text-green-700"
                            : isAccessible
                              ? "text-gray-700"
                              : "text-gray-400"
                      }`}
                    >
                      {step.title}
                    </div>
                    <div
                      className={`text-xs ${
                        isActive
                          ? "text-teal-100"
                          : isCompleted
                            ? "text-green-600"
                            : isAccessible
                              ? "text-gray-500"
                              : "text-gray-300"
                      }`}
                    >
                      Step {step.id} of {steps.length}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardContent className="p-8">
              <CurrentStepComponent
                data={agentData}
                onUpdate={updateAgentData}
                onNext={nextStep}
                onPrev={prevStep}
                onSave={handleSave}
                hasUnsavedChanges={hasUnsavedChanges}
                isEditMode={true}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Fixed Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
        <div className="max-w-7xl mx-auto flex justify-between">
          <Button
            variant="outline"
            onClick={() => navigate("/")}
            className="flex items-center gap-2 px-6 py-2 h-10 text-sm border-gray-300"
          >
            Cancel
          </Button>

          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-2 px-6 py-2 h-10 text-sm disabled:opacity-50 border-gray-300"
            >
              <ArrowLeft className="h-4 w-4" />
              Previous
            </Button>

            {currentStep === steps.length ? (
              <>
                {/* Deploy Step - Button sequence: Save Changes → Deploy → Test */}

                {/* Save Changes Button */}
                {hasUnsavedChanges ? (
                  <Button
                    onClick={handleSave}
                    disabled={updateTeamMutation.isPending}
                    className="flex items-center gap-2 px-6 py-2 bg-teal-800 hover:bg-teal-700 text-white h-10 text-sm disabled:opacity-50"
                  >
                    {updateTeamMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                ) : (
                  <div className="flex items-center gap-2 px-6 py-2 h-10 text-sm text-green-700">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Changes saved
                  </div>
                )}

                {/* Deploy Button - Available after save */}
                {!hasUnsavedChanges &&
                  !agentData?.deployStepActions?.isDeployed && (
                    <Button
                      onClick={() =>
                        agentData?.deployStepActions?.handleDeploy()
                      }
                      disabled={agentData?.deployStepActions?.isDeploying}
                      className="flex items-center gap-2 px-6 py-2 bg-teal-800 hover:bg-teal-700 text-white h-10 text-sm disabled:opacity-50"
                    >
                      {agentData?.deployStepActions?.isDeploying ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Deploying...
                        </>
                      ) : (
                        <>
                          <Rocket className="h-4 w-4" />
                          Deploy
                        </>
                      )}
                    </Button>
                  )}

                {/* Deployed Status */}
                {agentData?.deployStepActions?.isDeployed && (
                  <div className="flex items-center gap-2 px-6 py-2 h-10 text-sm text-green-700">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Deployed
                  </div>
                )}

                {/* Test Button - Available after deployment */}
                {agentData?.deployStepActions?.isDeployed && (
                  <Link to={`/workflows/${agentData.teamId}/chat/sessions`}>
                    <Button
                      variant="outline"
                      disabled={agentData?.deployStepActions?.isTesting}
                      className="flex items-center gap-2 px-6 py-2 h-10 text-sm disabled:opacity-50"
                    >
                      {agentData?.deployStepActions?.isTesting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Testing...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Playground
                        </>
                      )}
                    </Button>
                  </Link>
                )}
              </>
            ) : (
              <Button
                onClick={nextStep}
                className="flex items-center gap-2 px-6 py-2 bg-teal-800 hover:bg-teal-700 text-white h-10 text-sm"
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditTeam;
