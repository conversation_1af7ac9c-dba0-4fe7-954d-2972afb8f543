import React from "react";
import { Plus, Loader2, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ModelConfigurationDrawer } from "@/components/drawers/ModelConfigurationDrawer";
import { ModelGrid, ModelSearch, ModelPagination } from "@/components/models";
import { ModelProvider, useModelContext } from "@/contexts/ModelContext";
import { Model2 } from "@/types/model";

const ModelsManagementContent = () => {
  const {
    models,
    totalModels,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteModel,
    refetchModels,
  } = useModelContext();

  const [selectedModel, setSelectedModel] = React.useState<Model2 | null>(null);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [isForking, setIsForking] = React.useState(false); // Track if we're forking a model
  const [deletingModelId, setDeletingModelId] = React.useState<string | null>(
    null,
  );

  const handleCardClick = (model: Model2) => {
    // Only allow editing for non-default models
    if (!model.is_default) {
      setSelectedModel(model);
      setIsForking(false);
      setDrawerOpen(true);
    }
  };

  const handleFork = (model: Model2) => {
    // For default models, open the drawer in fork mode (create mode with pre-filled data)
    setSelectedModel(model);
    setIsForking(true);
    setDrawerOpen(true);
  };

  const handleAddNew = () => {
    setSelectedModel(null);
    setIsForking(false);
    setDrawerOpen(true);
  };

  const handleDelete = async (modelId: string) => {
    setDeletingModelId(modelId);
    try {
      await deleteModel(modelId);
    } finally {
      setDeletingModelId(null);
    }
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedModel(null);
    setIsForking(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-teal-600" />
          <span className="ml-2 text-slate-600">Loading models...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-600 text-xl font-semibold mb-3">
            Error loading models
          </div>
          <p className="text-slate-400 mb-8 text-base">
            {error.message || "Something went wrong"}
          </p>
          <Button
            onClick={() => refetchModels()}
            className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">Models</h1>
            <p className="text-slate-600 text-lg">
              Manage and configure your AI models
            </p>
          </div>
          <Button
            onClick={handleAddNew}
            className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm"
          >
            <Plus className="h-5 w-5" />
            Add New Model
          </Button>
        </div>
      </div>

      <div className="flex gap-4 mb-8">
        <ModelSearch
          isSearching={isSearching}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />
      </div>

      <ModelGrid
        models={models}
        isLoading={isLoading}
        isSearching={isSearching}
        isPaginating={isPaginating}
        searchTerm={searchTerm}
        onModelClick={handleCardClick}
        onModelDelete={handleDelete}
        onModelFork={handleFork}
        deletingModelId={deletingModelId || undefined}
      />
      <ModelPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalModels={totalModels}
        onPageChange={setCurrentPage}
        isPaginating={isPaginating}
      />

      {models.length === 0 && (
        <div className="text-center py-20">
          <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-8 border-2 border-slate-200">
            <Search className="h-12 w-12 text-slate-400" />
          </div>
          <div className="text-slate-500 text-xl font-semibold mb-3">
            No models found
          </div>

          <p className="text-slate-400 mb-8 text-base">
            {searchTerm.trim()
              ? `No models match your search criteria ${searchTerm}. `
              : ""}
            <br />
            Try adjusting your search terms or create your first model
          </p>
          {!searchTerm.trim() && (
            <Button
              onClick={handleAddNew}
              className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
            >
              <Plus className="h-5 w-5 mr-2" />
              Create Your First Model
            </Button>
          )}
        </div>
      )}

      <ModelConfigurationDrawer
        isOpen={drawerOpen}
        onClose={handleDrawerClose}
        model={isForking ? null : selectedModel}
        modelToFork={isForking ? selectedModel : null}
      />
    </div>
  );
};

const ModelsManagement = () => (
  <ModelProvider>
    <ModelsManagementContent />
  </ModelProvider>
);

export default ModelsManagement;
