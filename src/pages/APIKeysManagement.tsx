import React, { useState, useRef } from "react";
import { APIKeyProvider, useAPIKeys } from "../contexts/APIKeyContext";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "../components/ui/card";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Input } from "../components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "../components/ui/dialog";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "../components/ui/table";
import { APIKeyResponse } from "../types/api-key";
import CopyButton from "@/components/app/copy-button";
import { Plus, Trash, Info, Key } from "lucide-react";
import { DeleteConfirmationDialog } from "@/components/DeleteConfirmation";
import { DialogDescription } from "@radix-ui/react-dialog";

import { <PERSON>rollA<PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { beautifyDate } from "@/lib/datetime";
import {
  CreateAPIKeyModal,
  APIKeysTableSkeleton,
  ScopesTooltip,
} from "@/components/apikeys";

const APIKeysManagement: React.FC = () => {
  const {
    apiKeys,
    isLoadingKeys: isLoading,
    scopes: scopesData,
    isDeleting,
    keysError: error,
    createAPIKey,
    deleteAPIKey,
  } = useAPIKeys();

  const formRef = useRef<HTMLFormElement>(null);
  const [selectedScopes, setSelectedScopes] = useState<string[]>(["*"]);
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);

  const [showKeyModal, setShowKeyModal] = useState(false);
  const [createdKey, setCreatedKey] = useState<string | null>(null);
  const keyInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);
    createAPIKey({
      name: formData.get("name") as string,
      scopes: selectedScopes.length == 0 ? ["*"] : selectedScopes,
    })
      .then((data: APIKeyResponse) => {
        setShowKeyModal(true);
        setCreatedKey(data.key);
        setModalOpen(false);
      })
      .catch((error) => {
        console.error("Failed to create API key:", error);
      });
  };

  return (
    <div className="min-w-fit mx-auto p-6">
      <Card>
        <CardHeader className="max-w-fit flex flex-col gap-2">
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" /> <span>API Keys</span>
          </CardTitle>
          <CardDescription>
            Your secret API keys are listed below. Please note that we do not
            display your secret API keys again after you generate them. <br />
            Do not share your API key with others, or expose it in the browser
            or other client-side code.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-6">
            <CreateAPIKeyModal
              formRef={formRef}
              handleSubmit={handleSubmit}
              open={modalOpen}
              setOpen={setModalOpen}
              selectedScopes={selectedScopes}
              setSelectedScopes={setSelectedScopes}
            >
              <Button className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center font-medium transition-colors rounded-lg py-[10px] px-[16px] text-sm">
                <Plus className="h-5 w-5" />
                Create New Token
              </Button>
            </CreateAPIKeyModal>
          </div>

          {isLoading ? (
            <APIKeysTableSkeleton />
          ) : (
            <>
              {error && (
                <div className="text-red-500">
                  Error:{" "}
                  {typeof error === "object" && error && "message" in error
                    ? (error as { message?: string }).message
                    : "Failed to load API keys."}
                </div>
              )}
              {apiKeys.length === 0 && <div>No API keys found.</div>}
              {apiKeys.length > 0 && (
                <ScrollArea className="w-full overflow-x-auto">
                  <div className="min-w-[1000px]">
                    <Table className="overlfow-x-auto min-w-[1000px] w-full">
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Key</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Last Used</TableHead>
                          <TableHead>Total Requests</TableHead>
                          <TableHead>Delete</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {apiKeys.map((key) => (
                          <TableRow key={key.id}>
                            <TableCell className="flex items-center gap-2">
                              <span>{key.name}</span>
                              {(key.description ||
                                (key.scopes && key.scopes.length > 0)) && (
                                <ScopesTooltip api_key={key}>
                                  <span className="cursor-pointer">
                                    <Info className="h-4 w-4 text-slate-400" />
                                  </span>
                                </ScopesTooltip>
                              )}
                            </TableCell>
                            <TableCell>
                              {key.key_prefix.slice(0, 5)}*******************
                            </TableCell>
                            <TableCell>
                              {key.created_at
                                ? beautifyDate(key.created_at)
                                : "-"}
                            </TableCell>
                            <TableCell>
                              {key.last_used_at
                                ? beautifyDate(key.last_used_at)
                                : "Never Used"}
                            </TableCell>
                            <TableCell>{key.total_requests ?? 0}</TableCell>
                            <TableCell>
                              <DeleteConfirmationDialog
                                open={deleteOpen}
                                onOpenChange={setDeleteOpen}
                                title="Delete API Key"
                                text="Are you sure you want to delete this API key? This action cannot be undone."
                                onConfirm={() => deleteAPIKey(key.id)}
                              >
                                <Button variant="ghost">
                                  <Trash className="h-4 w-4 text-red-500" />
                                </Button>
                              </DeleteConfirmationDialog>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <ScrollBar orientation="horizontal" />
                  </div>
                </ScrollArea>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <Dialog open={showKeyModal} onOpenChange={setShowKeyModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>API Key Created</DialogTitle>
            <DialogDescription>
              Copy and save your new API key. You won't be able to see it again!
            </DialogDescription>
          </DialogHeader>
          <div className="flex gap-2 items-center">
            <Input
              ref={keyInputRef}
              value={createdKey || ""}
              readOnly
              className="bg-muted text-muted-foreground flex-1 font-mono"
            />
            <CopyButton data={createdKey || ""} />
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                className="bg-teal-800 hover:bg-teal-700 text-white"
                type="button"
              >
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default function APIKeysManagementWrapped() {
  return (
    <APIKeyProvider>
      <div className="flex flex-col items-center min-h-screen bg-muted">
        <div className="w-full max-w-7xl p-6">
          <APIKeysManagement />
        </div>
      </div>
    </APIKeyProvider>
  );
}
