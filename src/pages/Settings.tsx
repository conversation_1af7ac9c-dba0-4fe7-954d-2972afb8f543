import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { useCurrentUser } from "@/hooks/useUser";
import { ProfileUpdateForm } from "@/components/settings/ProfileUpdateForm";
import { PasswordChangeForm } from "@/components/settings/PasswordChangeForm";

const Settings = () => {
  // Query for current user data
  const { data: user, isLoading, error } = useCurrentUser();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load user data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage your account information and security settings.
        </p>
      </div>

      <div className="space-y-6">
        {/* Profile Information Section */}
        <ProfileUpdateForm user={user} />

        {/* Password Security Section */}
        <PasswordChangeForm />
      </div>
    </div>
  );
};

export default Settings;
