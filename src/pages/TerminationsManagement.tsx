import React from "react";
import { Plus, Loader2, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { TerminationConfigurationDrawer } from "@/components/drawers/TerminationConfigurationDrawer";
import { Termination2 } from "@/types/termination";
import {
  TerminationSearch,
  TerminationGrid,
  TerminationPagination,
} from "@/components/terminations";
import {
  TerminationsProvider,
  useTerminationsContext,
} from "@/contexts/TerminationsContext";

const TerminationsManagementContent = () => {
  const {
    terminations,
    totalTerminations,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteTermination,
    refetchTerminations,
  } = useTerminationsContext();

  const [selectedTermination, setSelectedTermination] =
    React.useState<Termination2 | null>(null);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [isForking, setIsForking] = React.useState(false); // Track if we're forking a termination condition
  const [deletingTerminationId, setDeletingTerminationId] = React.useState<
    string | null
  >(null);

  const handleCardClick = (termination: Termination2) => {
    // Only allow editing for non-default termination conditions
    if (!termination.is_default) {
      setSelectedTermination(termination);
      setIsForking(false);
      setDrawerOpen(true);
    }
  };

  const handleFork = (termination: Termination2) => {
    // For default termination conditions, open the drawer in fork mode (create mode with pre-filled data)
    setSelectedTermination(termination);
    setIsForking(true);
    setDrawerOpen(true);
  };

  const handleAddNew = () => {
    setSelectedTermination(null);
    setIsForking(false);
    setDrawerOpen(true);
  };

  const handleDelete = async (terminationId: string) => {
    setDeletingTerminationId(terminationId);
    try {
      await deleteTermination(terminationId);
    } finally {
      setDeletingTerminationId(null);
    }
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedTermination(null);
    setIsForking(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-teal-800" />
          <span className="ml-2 text-slate-600">
            Loading termination conditions...
          </span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-600 text-xl font-semibold mb-3">
            Error loading termination conditions
          </div>
          <p className="text-slate-400 mb-8 text-base">
            {error?.message || "Something went wrong"}
          </p>
          <Button
            onClick={() => refetchTerminations()}
            className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">
              Termination Conditions
            </h1>
            <p className="text-slate-600 text-lg">
              Manage and configure termination conditions for your agents
            </p>
          </div>
          <Button
            onClick={handleAddNew}
            className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm"
          >
            <Plus className="h-5 w-5" />
            Add New Termination Condition
          </Button>
        </div>
      </div>

      <div className="flex gap-4 mb-8">
        <TerminationSearch
          isSearching={isSearching}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />
      </div>

      <TerminationGrid
        terminations={terminations}
        isLoading={isLoading}
        isSearching={isSearching}
        isPaginating={isPaginating}
        searchTerm={searchTerm}
        onTerminationClick={handleCardClick}
        onTerminationDelete={handleDelete}
        onTerminationFork={handleFork}
        deletingTerminationId={deletingTerminationId || undefined}
      />

      <TerminationPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalTerminations={totalTerminations}
        onPageChange={setCurrentPage}
        isPaginating={isPaginating}
      />

      {terminations.length === 0 && (
        <div className="text-center py-20">
          <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-8 border-2 border-slate-200">
            <Search className="h-12 w-12 text-slate-400" />
          </div>
          <div className="text-slate-500 text-xl font-semibold mb-3">
            No termination conditions found
          </div>
          <p className="text-slate-400 mb-8 text-base">
            Try adjusting your search terms or create your first termination
            condition
          </p>
          <Button
            onClick={handleAddNew}
            className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
          >
            <Plus className="h-5 w-5 mr-2" />
            Create Your First Termination Condition
          </Button>
        </div>
      )}

      <TerminationConfigurationDrawer
        isOpen={drawerOpen}
        onClose={handleDrawerClose}
        termination={isForking ? null : selectedTermination}
        terminationToFork={isForking ? selectedTermination : null}
      />
    </div>
  );
};

const TerminationsManagement = () => (
  <TerminationsProvider>
    <TerminationsManagementContent />
  </TerminationsProvider>
);

export default TerminationsManagement;
