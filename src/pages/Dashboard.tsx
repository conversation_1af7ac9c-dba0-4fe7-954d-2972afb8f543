import { useState } from "react";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Copy,
  Trash2,
  MoreVertical,
  Play,
  Users,
  Loader2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Link, useNavigate } from "react-router-dom";
import { Team2 } from "../types/team";
import { useToast } from "@/hooks/use-toast";
import { useWorkflow } from "@/contexts/WorkflowContext";
import { SkeletonGrid } from "@/components/ui/skeleton-card";
import { useCreateTeam } from "@/hooks/useTeams";
import { DeleteWorkflowModal } from "@/components/modals/DeleteWorkflowModal";

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Use WorkflowContext for all team data and state management
  const {
    teams,
    totalTeams,
    activeWorkflow,
    totalWorkflow,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteTeam: contextDeleteTeam,
    addNewTeam,
  } = useWorkflow();

  // Initialize the create team mutation for duplicate functionality
  const createTeamMutation = useCreateTeam(addNewTeam);

  // Track which team is being duplicated for loading state
  const [duplicatingTeamId, setDuplicatingTeamId] = useState<string | null>(
    null,
  );

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] = useState<Team2 | null>(null);

  // Dropdown menu state tracking
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-50 text-green-700 border-green-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  const handleTeamCardClick = (team: Team2) => {
    navigate(`/team/edit/${team.id}`, { state: { teamData: team } });
  };

  // Open delete confirmation modal
  const handleDeleteTeam = (team: Team2) => {
    // Close the dropdown first
    setOpenDropdownId(null);

    // Small delay to allow dropdown to close before opening modal
    setTimeout(() => {
      setWorkflowToDelete(team);
      setIsDeleteModalOpen(true);
    }, 100);
  };

  // Actual deletion function called by the modal
  const performDeleteTeam = async (teamId: string, teamName: string) => {
    return await contextDeleteTeam(teamId, teamName);
  };

  // Close delete modal
  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setWorkflowToDelete(null);
    // Ensure dropdown is closed
    setOpenDropdownId(null);
  };

  // Duplicate team function
  const handleDuplicateTeam = async (teamId: string, teamName: string) => {
    // Prevent multiple duplications at once
    if (duplicatingTeamId) {
      return;
    }

    // Close the dropdown first
    setOpenDropdownId(null);

    // Set loading state for this specific team
    setDuplicatingTeamId(teamId);

    try {
      // Fetch the complete team data using the API directly
      const { teamsApi } = await import("@/api/teams");
      const teamResponse = await teamsApi.getById(teamId);

      if (!teamResponse) {
        toast({
          title: "Error",
          description: "Team data not available for duplication.",
          variant: "destructive",
        });
        return;
      }

      // Create a payload similar to CreateTeam.tsx saveChanges function
      const originalComponent = teamResponse.component;

      // Create the duplicate payload with the same structure as CreateTeam
      const payload: any = {
        component: {
          component_type: "team" as const,
          component_version: 1,
          config: {
            // Copy all config from original team
            ...originalComponent?.config,
          },
          description: `${originalComponent?.description || "A team workflow"} (Copy)`,
          label: `${originalComponent?.label || "Workflow Team"} (Copy)`,
          provider:
            originalComponent?.provider ||
            "autogen_agentchat.teams.RoundRobinGroupChat",
          version: 1,
        },
        organization_id: "123e4567-e89b-12d3-a456-426614174000", // TODO: Get from user context
        model_id: (teamResponse as any).model_id,
      };

      // Include agent IDs if they exist
      const teamAgentIds = (teamResponse as any).team_agent_ids;
      if (
        Array.isArray(teamAgentIds) &&
        teamAgentIds.filter(Boolean).length > 0
      ) {
        payload.team_agent_ids = teamAgentIds.filter(Boolean);
      }

      // Include input IDs if they exist
      const teamInputIds = (teamResponse as any).team_input_ids;
      if (
        Array.isArray(teamInputIds) &&
        teamInputIds.filter(Boolean).length > 0
      ) {
        payload.team_input_ids = teamInputIds.filter(Boolean);
      }

      // Include output IDs if they exist
      const teamOutputIds = (teamResponse as any).team_output_ids;
      if (
        Array.isArray(teamOutputIds) &&
        teamOutputIds.filter(Boolean).length > 0
      ) {
        payload.team_output_ids = teamOutputIds.filter(Boolean);
      }

      // Include termination condition IDs if they exist
      const teamTerminationConditionIds = (teamResponse as any)
        .team_termination_condition_ids;
      if (
        Array.isArray(teamTerminationConditionIds) &&
        teamTerminationConditionIds.filter(Boolean).length > 0
      ) {
        payload.team_termination_condition_ids =
          teamTerminationConditionIds.filter(Boolean);
      }

      // Send the POST request to create duplicate
      const result = await createTeamMutation.mutateAsync(payload);

      toast({
        title: "Team Duplicated",
        description: `Team "${teamName}" has been successfully duplicated.`,
      });

      // Redirect to edit page of the new duplicated team
      navigate(`/team/edit/${result.id}`);
    } catch (error: any) {
      console.error("Failed to duplicate team:", error);
      toast({
        title: "Duplication Failed",
        description:
          error?.message ||
          "There was an error duplicating your team. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Clear loading state
      setDuplicatingTeamId(null);
    }
  };

  // Error state
  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-500 text-xl font-semibold mb-3">
            Error loading teams
          </div>
          <p className="text-slate-400 mb-8 text-base">
            Please try refreshing the page
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      {/* Header with Stats */}
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">
              Workflows
            </h1>
            <p className="text-slate-600 text-lg">
              Manage and deploy your intelligent workflows
            </p>
          </div>
          <Link to="/create">
            <Button className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm">
              <Plus className="h-5 w-5" />
              Create New Workflow
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-white border-slate-200 border-2">
            <CardContent className="p-8">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-700 text-sm font-semibold mb-2">
                    Total Workflows
                  </p>
                  <p className="text-4xl font-bold text-slate-900">
                    {totalWorkflow}
                  </p>
                </div>
                <div className="w-14 h-14 bg-slate-100 rounded-xl flex items-center justify-center border-2 border-slate-200">
                  <Users className="h-7 w-7 text-slate-700" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-slate-200 border-2">
            <CardContent className="p-8">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-700 text-sm font-semibold mb-2">
                    Active Workflows
                  </p>
                  <p className="text-4xl font-bold text-slate-900">
                    {activeWorkflow}
                  </p>
                </div>
                <div className="w-14 h-14 bg-slate-100 rounded-xl flex items-center justify-center border-2 border-slate-200">
                  <Play className="h-7 w-7 text-slate-700" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Enhanced Filters */}
      <div className="flex gap-4 mb-8">
        <div className="relative flex-1 max-w-md">
          {isSearching ? (
            <Loader2 className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5 animate-spin" />
          ) : (
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
          )}
          <Input
            placeholder="Search workflows by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-12 h-14 border-slate-300 focus:border-teal-500 focus:ring-teal-500 text-base"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48 h-14 border-slate-300 text-base">
            <Filter className="h-5 w-5 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Deployed</SelectItem>
            <SelectItem value="inactive">Undeployed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Enhanced Team Cards */}
      {isLoading || isSearching || isPaginating ? (
        <SkeletonGrid count={9} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teams.map((team: Team2) => (
            <Card
              key={team.id}
              className="hover:shadow-xl hover:shadow-slate-200/50 transition-all duration-300 bg-white border-slate-200 border overflow-hidden cursor-pointer group relative"
              onClick={() => handleTeamCardClick(team)}
            >
              <CardContent className="p-0">
                {/* Card Header with Actions */}
                <div className="p-6 pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <h3 className="text-xl font-bold text-slate-900 truncate">
                        {team.name}
                      </h3>
                      {team.status === "Active" && (
                        <Badge
                          className={`${getStatusColor(team.status)} border font-medium px-2 py-1 text-xs shrink-0`}
                          variant="default"
                        >
                          Deployed
                        </Badge>
                      )}
                    </div>

                    {/* Action Menu - Always visible but subtle */}
                    <div
                      className="opacity-60 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <DropdownMenu
                        open={openDropdownId === team.id}
                        onOpenChange={(open) =>
                          setOpenDropdownId(open ? team.id : null)
                        }
                      >
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-slate-100"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleTeamCardClick(team)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDuplicateTeam(team.id, team.name);
                            }}
                            disabled={duplicatingTeamId === team.id}
                          >
                            {duplicatingTeamId === team.id ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Duplicating...
                              </>
                            ) : (
                              <>
                                <Copy className="h-4 w-4 mr-2" />
                                Duplicate
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteTeam(team);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-600 text-sm leading-relaxed line-clamp-2 mb-4">
                    {team.description}
                  </p>
                </div>

                {/* Card Footer with Metadata and Actions */}
                <div className="px-6 pb-6">
                  <div className="bg-slate-50 rounded-lg p-4 space-y-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs text-slate-600">
                        <span className="flex items-center gap-1">
                          <span className="font-medium">Type:</span>
                          <span className="text-slate-800 font-medium">
                            {team.teamType}
                          </span>
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span className="font-medium">
                            {team.participantsCount}
                          </span>
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs text-slate-600">
                        <span>
                          Max turns:{" "}
                          <span className="text-slate-800 font-medium">
                            {team.maxTurns}
                          </span>
                        </span>
                        <span className="text-slate-500">
                          Modified {team.lastModified}
                        </span>
                      </div>
                    </div>

                    {/* Action Button */}
                    {team.status === "Active" && (
                      <div
                        className="pt-2 border-t border-slate-200"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Link to={`/workflows/${team.id}/chat/sessions`} className="block">
                          <Button
                            variant="outline"
                            className="w-full border-teal-600 text-teal-600 hover:bg-teal-50 hover:border-teal-700 hover:text-teal-700 h-9 text-sm font-medium transition-colors"
                            size="sm"
                          >
                            <Play className="h-4 w-4 mr-2" />
                            Playground
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>

                {/* Hover Overlay for Quick Actions */}
                <div className="absolute inset-0 bg-slate-900/5 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-8">
          <div className="text-sm text-slate-600">
            {/* Showing {teams.length} of {totalTeams} workflows */}
          </div>

          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage(currentPage - 1)}
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {/* Show page numbers with ellipsis for large page counts */}
              {totalPages <= 7 ? (
                // Show all pages if 7 or fewer
                Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ),
                )
              ) : (
                // Show ellipsis for large page counts
                <>
                  {currentPage <= 4 ? (
                    <>
                      {Array.from({ length: 5 }, (_, i) => i + 1).map(
                        (page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ),
                      )}
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          className="cursor-pointer"
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  ) : currentPage >= totalPages - 3 ? (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(1)}
                          className="cursor-pointer"
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      {Array.from(
                        { length: 5 },
                        (_, i) => totalPages - 4 + i,
                      ).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                    </>
                  ) : (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(1)}
                          className="cursor-pointer"
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      {Array.from(
                        { length: 3 },
                        (_, i) => currentPage - 1 + i,
                      ).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          className="cursor-pointer"
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}
                </>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage(currentPage + 1)}
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* No Search Results State */}
      {teams.length === 0 &&
        !isLoading &&
        !isSearching &&
        !isPaginating &&
        (searchTerm.trim() !== "" || statusFilter !== "all") && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-8 border-2 border-slate-200">
              <Search className="h-12 w-12 text-slate-400" />
            </div>
            <div className="text-slate-500 text-xl font-semibold mb-3">
              No workflows found for "{searchTerm.trim() || "your search"}"
            </div>
            <p className="text-slate-400 mb-8 text-base">
              Try adjusting your search terms or filters to find what you're
              looking for
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                }}
                className="border-slate-300 text-slate-600 hover:bg-slate-50"
              >
                Clear Search
              </Button>
              <Link to="/create">
                <Button className="bg-teal-800 hover:bg-teal-700 text-white">
                  <Plus className="h-5 w-5 mr-2" />
                  Create New Workflow
                </Button>
              </Link>
            </div>
          </div>
        )}

      {/* General Empty State (when no search/filter is active) */}
      {teams.length === 0 &&
        !isLoading &&
        !isSearching &&
        !isPaginating &&
        searchTerm.trim() === "" &&
        statusFilter === "all" && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-8 border-2 border-slate-200">
              <Users className="h-12 w-12 text-slate-400" />
            </div>
            <div className="text-slate-500 text-xl font-semibold mb-3">
              No workflows yet
            </div>
            <p className="text-slate-400 mb-8 text-base">
              Get started by creating your first workflow
            </p>
            <Link to="/create">
              <Button className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base">
                <Plus className="h-5 w-5 mr-2" />
                Create Your First Workflow
              </Button>
            </Link>
          </div>
        )}

      {/* Delete Confirmation Modal */}
      <DeleteWorkflowModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        workflow={workflowToDelete}
        onDelete={performDeleteTeam}
      />
    </div>
  );
};

export default Dashboard;
