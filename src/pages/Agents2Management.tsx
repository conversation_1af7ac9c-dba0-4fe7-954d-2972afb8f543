import { useState } from "react";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Agents2ConfigurationDrawer } from "@/components/drawers/Agents2ConfigurationDrawer";
import { AgentGrid } from "@/components/agents/AgentGrid";
import { AgentSearch } from "@/components/agents/AgentSearch";
import { AgentPagination } from "@/components/agents/AgentPagination";
import { useAgent } from "@/contexts/AgentContext";
import { Agent2 } from "@/types/agent";
import { useToast } from "@/hooks/use-toast";

const Agents2Management = () => {
  const [selectedAgent, setSelectedAgent] = useState<Agent2 | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [isForking, setIsForking] = useState(false); // Track if we're forking an agent
  const [deletingAgentId, setDeletingAgentId] = useState<string | null>(null);
  const { toast } = useToast();

  // Use AgentContext for all agent data and state management
  const {
    agents,
    totalAgents,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteAgent: contextDeleteAgent,
    refetchAgents,
  } = useAgent();

  const handleCardClick = (agent: Agent2) => {
    // Only allow editing for non-default agents
    if (!agent.is_default) {
      setSelectedAgent(agent);
      setIsForking(false);
      setDrawerOpen(true);
    }
  };

  const handleFork = (agent: Agent2) => {
    // For default agents, open the drawer in fork mode (create mode with pre-filled data)
    setSelectedAgent(agent);
    setIsForking(true);
    setDrawerOpen(true);
  };

  const handleAddNew = () => {
    setSelectedAgent(null);
    setIsForking(false);
    setDrawerOpen(true);
  };

  const handleDelete = async (agentId: string) => {
    const agent = agents.find((a) => a.id === agentId);
    if (!agent) return;

    setDeletingAgentId(agentId);
    try {
      await contextDeleteAgent(agentId, agent.name);
      toast({
        title: "Agent deleted",
        description: `${agent.name} has been successfully deleted.`,
      });
    } catch (error) {
      console.error("Failed to delete agent:", error);
      toast({
        title: "Error",
        description: "Failed to delete agent. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeletingAgentId(null);
    }
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedAgent(null);
    setIsForking(false);
  };

  if (error) {
    return (
      <div className="p-8 max-w-7xl mx-auto">
        <div className="text-center py-20">
          <div className="text-red-600 text-xl font-semibold mb-3">Error loading agents</div>
          <p className="text-slate-400 mb-8 text-base">{(error as any)?.message || "Something went wrong"}</p>
          <Button
            onClick={() => refetchAgents()}
            className="bg-teal-800 hover:bg-teal-700 text-white px-8 py-4 h-auto rounded-lg font-semibold text-base"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">Agents</h1>
            <p className="text-slate-600 text-lg">Manage and configure your specialized AI agents</p>
          </div>
          <Button
            onClick={handleAddNew}
            className="bg-teal-800 hover:bg-teal-700 text-white h-auto flex items-center gap-3 font-semibold transition-colors rounded-lg py-[10px] px-[16px] text-sm"
          >
            <Plus className="h-5 w-5" />
            Add New Agent
          </Button>
        </div>
      </div>

      <div className="mb-8">
        <AgentSearch searchTerm={searchTerm} onSearchChange={setSearchTerm} isSearching={isSearching} />
      </div>

      <AgentGrid
        agents={agents}
        isLoading={isLoading}
        isSearching={isSearching}
        isPaginating={isPaginating}
        searchTerm={searchTerm}
        onAgentClick={handleCardClick}
        onAgentDelete={handleDelete}
        onAgentFork={handleFork}
        deletingAgentId={deletingAgentId}
      />

      <AgentPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalAgents={totalAgents}
        onPageChange={setCurrentPage}
        isPaginating={isPaginating}
      />

      <Agents2ConfigurationDrawer
        isOpen={drawerOpen}
        onClose={handleDrawerClose}
        agent={isForking ? null : selectedAgent}
        agentToFork={isForking ? selectedAgent : null}
      />
    </div>
  );
};

export default Agents2Management;
