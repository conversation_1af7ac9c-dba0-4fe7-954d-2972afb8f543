import { More<PERSON><PERSON><PERSON>, Trash2, Edit, GitFork } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Model2 } from "@/types/model";

interface ModelCardProps {
  model: Model2;
  onClick: (model: Model2) => void;
  onDelete: (modelId: string) => void;
  onFork?: (model: Model2) => void; // New prop for forking default models
  isDeleting?: boolean;
}

export const ModelCard: React.FC<ModelCardProps> = ({
  model,
  onClick,
  onDelete,
  onFork,
  isDeleting = false,
}) => {
  const handleCardClick = () => {
    // Only allow clicking for non-default models
    if (!model.is_default) {
      onClick(model);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(model.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(model);
  };

  const handleFork = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFork) {
      onFork(model);
    }
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 bg-white border-slate-200 border-2 overflow-hidden group h-[240px] ${
        model.is_default ? "cursor-default" : "cursor-pointer"
      }`}
      onClick={handleCardClick}
    >
      <CardContent className="p-6 flex flex-col h-full justify-between">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex flex-col gap-2 max-w-[85%]">
              <h3 className="text-lg font-bold text-slate-900 transition-colors leading-tight truncate">
                {model.name}
              </h3>
              <div className="flex flex-row gap-2">
                {model.is_default && (
                  <Badge
                    variant="secondary"
                    className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs"
                  >
                    Default
                  </Badge>
                )}
                {/* <Badge variant="secondary" className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs">
                  {model.component.provider.split(".").at(-1)}
                </Badge> */}
                <Badge
                  variant="secondary"
                  className="w-fit bg-green-50 text-green-700 border-green-200 text-xs"
                >
                  {model.modelId}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
              {model.description || "No description available"}
            </p>
            <div className="flex gap-2 items-center text-xs text-slate-500"></div>
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="text-xs text-slate-400">
            Modified {model.lastModified}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-slate-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {model.is_default ? (
                // For default models: show Fork & Edit option
                <DropdownMenuItem onClick={handleFork}>
                  <GitFork className="h-4 w-4 mr-2" />
                  Fork & Edit
                </DropdownMenuItem>
              ) : (
                // For non-default models: show Edit and Delete options
                <>
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeleting ? "Deleting..." : "Delete"}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};
