import { useState, useEffect } from "react";
import { X, Save, Loader2, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useTerminationConditionConfigs,
  useCreateTermination,
  useUpdateTermination,
} from "@/hooks/useTerminations";
import { useValidateComponent } from "@/hooks/useValidation";
import { ValidationResults } from "@/components/ui/validation-results";
import {
  Termination2,
  TerminationConditionComponent,
} from "@/types/termination";
import { ComponentModel, ValidationResponse } from "@/types/validation";
import { toast } from "@/hooks/use-toast";
import { makeIdentifier } from "@/lib/utils";

interface TerminationConfigurationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  termination: Termination2 | null;
  terminationToFork?: Termination2 | null; // Optional prop for forking default termination conditions
}

export const TerminationConfigurationDrawer = ({
  isOpen,
  onClose,
  termination,
  terminationToFork,
}: TerminationConfigurationDrawerProps) => {
  const [selectedConfigType, setSelectedConfigType] = useState<string>("");
  const [formData, setFormData] = useState({
    label: "",
    description: "",
    terminationText: "",
    maxMessages: 10,
    includeAgentEvent: false,
    timeout: 1000,
    functionName: "",
    target: "",
    maxTotalToken: 100,
    maxPromptToken: 100,
    maxCompletionToken: 100,
    source: "",
    sources: "",
  });

  // Load available termination condition configurations
  const { data: terminationConfigs, isLoading: configsLoading } =
    useTerminationConditionConfigs();

  // Mutations
  const createMutation = useCreateTermination();
  const updateMutation = useUpdateTermination();
  const validateComponentMutation = useValidateComponent();

  // Validation state
  const [validationResult, setValidationResult] =
    useState<ValidationResponse | null>(null);

  const isEditing = !!termination;
  const isForkMode = !!terminationToFork;

  // Initialize form data when termination changes or drawer opens/closes
  useEffect(() => {
    if (termination) {
      const config = termination.config || {};
      setFormData({
        label: termination.component?.label || "",
        description: termination.description || "",
        terminationText: (config.text as string) || "",
        maxMessages: (config.max_messages as number) || 10,
        includeAgentEvent: (config.include_agent_event as boolean) || false,
        timeout: (config.timeout_seconds as number) || 1000,
        functionName: (config.function_name as string) || "",
        target: (config.target as string) || "",
        maxTotalToken: (config.max_total_token as number) || 100,
        maxPromptToken: (config.max_prompt_token as number) || 100,
        maxCompletionToken: (config.max_completion_token as number) || 100,
        source: (config.source as string) || "",
        sources: Array.isArray(config.sources)
          ? (config.sources as string[]).join(", ")
          : "",
      });
      setSelectedConfigType(termination.component?.label || "");
    } else if (terminationToFork && isForkMode) {
      // Populate form with forked termination data (create new termination based on default)
      const config = terminationToFork.config || {};
      setFormData({
        label: `${terminationToFork.component?.label || "Forked Termination"} (Custom)`, // Add suffix to indicate it's a fork
        description: terminationToFork.description || "",
        terminationText: (config.text as string) || "",
        maxMessages: (config.max_messages as number) || 10,
        includeAgentEvent: (config.include_agent_event as boolean) || false,
        timeout: (config.timeout_seconds as number) || 1000,
        functionName: (config.function_name as string) || "",
        target: (config.target as string) || "",
        maxTotalToken: (config.max_total_token as number) || 100,
        maxPromptToken: (config.max_prompt_token as number) || 100,
        maxCompletionToken: (config.max_completion_token as number) || 100,
        source: (config.source as string) || "",
        sources: Array.isArray(config.sources)
          ? (config.sources as string[]).join(", ")
          : "",
      });
      setSelectedConfigType(terminationToFork.component?.label || "");
    } else if (!isEditing && !isForkMode) {
      // Reset form for new termination
      setFormData({
        label: "",
        description: "",
        terminationText: "",
        maxMessages: 10,
        includeAgentEvent: false,
        timeout: 1000,
        functionName: "",
        target: "",
        maxTotalToken: 100,
        maxPromptToken: 100,
        maxCompletionToken: 100,
        source: "",
        sources: "",
      });
      setSelectedConfigType("");
    }
  }, [termination, terminationToFork, isForkMode, isEditing, isOpen]);

  // Clear validation when form data changes
  useEffect(() => {
    setValidationResult(null);
  }, [
    formData.label,
    formData.description,
    formData.terminationText,
    formData.maxMessages,
    formData.includeAgentEvent,
    formData.timeout,
    formData.functionName,
    formData.target,
    formData.maxTotalToken,
    formData.maxPromptToken,
    formData.maxCompletionToken,
    formData.source,
    formData.sources,
    selectedConfigType,
  ]);

  const getSelectedConfig = (): TerminationConditionComponent | null => {
    if (!terminationConfigs || !selectedConfigType) return null;
    return (
      terminationConfigs.find(
        (config) => config.label === selectedConfigType,
      ) || null
    );
  };

  const buildComponentData = () => {
    const selectedConfig = getSelectedConfig();
    if (!selectedConfig) return null;

    // Build config based on termination type
    let config: Record<string, any> = {
      name: makeIdentifier(formData.label), // Auto-generate Python identifier from label
    };

    switch (selectedConfig.label) {
      case "TextMentionTermination":
        config.text = formData.terminationText;
        break;
      case "MaxMessageTermination":
        config.max_messages = formData.maxMessages;
        config.include_agent_event = formData.includeAgentEvent;
        break;
      case "TimeoutTermination":
        config.timeout_seconds = formData.timeout;
        break;
      case "FunctionCallTermination":
        config.function_name = formData.functionName;
        break;
      case "HandoffTermination":
        config.target = formData.target;
        break;
      case "TokenUsageTermination":
        config.max_total_token = formData.maxTotalToken;
        config.max_prompt_token = formData.maxPromptToken;
        config.max_completion_token = formData.maxCompletionToken;
        break;
      case "SourceMatchTermination":
        config.sources = formData.sources
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s);
        break;
      case "TextMessageTermination":
        config.source = formData.source;
        break;
      case "StopMessageTermination":
      case "ExternalTermination":
        // These have empty configs
        break;
      case "OrTerminationCondition":
        // This is complex and would need special handling - skip for now
        break;
    }

    return {
      component: {
        ...selectedConfig,
        label: formData.label, // Set user-facing label
        description: formData.description, // Place description at component level
        config,
      },
    };
  };

  const createTerminationComponent = (): ComponentModel => {
    const selectedConfig = getSelectedConfig();
    if (!selectedConfig) {
      throw new Error("No termination configuration selected");
    }

    // Build config based on termination type
    let config: Record<string, any> = {
      name: makeIdentifier(formData.label), // Auto-generate Python identifier from label
    };

    switch (selectedConfig.label) {
      case "TextMentionTermination":
        config.text = formData.terminationText;
        break;
      case "MaxMessageTermination":
        config.max_messages = formData.maxMessages;
        config.include_agent_event = formData.includeAgentEvent;
        break;
      case "TimeoutTermination":
        config.timeout_seconds = formData.timeout;
        break;
      case "FunctionCallTermination":
        config.function_name = formData.functionName;
        break;
      case "HandoffTermination":
        config.target = formData.target;
        break;
      case "TokenUsageTermination":
        config.max_total_token = formData.maxTotalToken;
        config.max_prompt_token = formData.maxPromptToken;
        config.max_completion_token = formData.maxCompletionToken;
        break;
      case "SourceMatchTermination":
        config.sources = formData.sources
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s);
        break;
      case "TextMessageTermination":
        config.source = formData.source;
        break;
      case "StopMessageTermination":
      case "ExternalTermination":
        // These have empty configs
        break;
      case "OrTerminationCondition":
        // This is complex and would need special handling - skip for now
        break;
    }

    return {
      provider: selectedConfig.provider,
      component_type: "termination_condition",
      version: selectedConfig.version || 1,
      component_version: selectedConfig.component_version || 1,
      description:
        formData.description ||
        selectedConfig.description ||
        "Termination condition configuration",
      label: formData.label,
      config,
    };
  };

  const handleSave = async () => {
    if (!selectedConfigType) {
      toast({
        title: "Validation Error",
        description: "Please select a termination type",
        variant: "destructive",
      });
      return;
    }

    if (!formData.label.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a label",
        variant: "destructive",
      });
      return;
    }

    try {
      const component = createTerminationComponent();

      // Validate before saving
      const validationResult =
        await validateComponentMutation.mutateAsync(component);
      setValidationResult(validationResult);

      if (!validationResult.is_valid) {
        toast({
          title: "Validation Failed",
          description:
            validationResult.errors.map((error) => error.error).join("\n") +
            "\n\nPlease fix the validation errors before saving",
          variant: "destructive",
        });
        return;
      }

      const componentData = buildComponentData();
      if (!componentData) {
        toast({
          title: "Error",
          description: "Invalid configuration",
          variant: "destructive",
        });
        return;
      }

      if (isEditing && termination?.id && !isForkMode) {
        // Update existing termination condition (only if not forking)
        await updateMutation.mutateAsync({
          id: termination.id,
          data: componentData,
        });
        toast({
          title: "Success",
          description: "Termination condition updated successfully",
        });
      } else {
        // Create new termination condition (for new termination conditions or when forking)
        await createMutation.mutateAsync(componentData);
        toast({
          title: "Success",
          description: isForkMode
            ? "Termination condition forked successfully"
            : "Termination condition created successfully",
        });
      }
      onClose();
    } catch (error: any) {
      console.error("Error saving termination condition:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to save termination condition",
        variant: "destructive",
      });
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />

      <div className="fixed right-0 top-0 h-full w-1/2 min-w-[600px] max-w-[900px] bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out flex flex-col">
        <div className="flex-shrink-0 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between p-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {isForkMode
                  ? "Fork Termination Condition"
                  : isEditing
                    ? "Edit Termination Configuration"
                    : "Create Termination Configuration"}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {isForkMode
                  ? "Create a custom version based on the default termination condition"
                  : "Configure termination conditions and behavior"}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-10 w-10 p-0"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6">
              {/* Termination Type Selection */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 text-lg">
                  Termination Type
                </h4>

                <div className="space-y-2">
                  <Label htmlFor="terminationType">Type</Label>
                  {configsLoading ? (
                    <div className="flex items-center justify-center p-4">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="ml-2 text-sm text-gray-500">
                        Loading termination types...
                      </span>
                    </div>
                  ) : (
                    <Select
                      value={selectedConfigType}
                      onValueChange={setSelectedConfigType}
                      disabled={isEditing}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select termination type" />
                      </SelectTrigger>
                      <SelectContent>
                        {terminationConfigs?.map((config) => (
                          <SelectItem key={config.label} value={config.label}>
                            {config.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  {isEditing && (
                    <p className="text-xs text-gray-500">
                      Termination type cannot be changed when editing
                    </p>
                  )}
                </div>
              </div>

              {/* Component Details */}
              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h4 className="font-medium text-gray-900 text-lg">
                  Component Details
                </h4>

                <div className="space-y-2">
                  <Label htmlFor="label">Label</Label>
                  <Input
                    id="label"
                    value={formData.label}
                    onChange={(e) =>
                      setFormData({ ...formData, label: e.target.value })
                    }
                    placeholder="Termination condition label (e.g., Max Messages Stop)"
                  />
                  <p className="text-xs text-gray-500">
                    This is the display name for your termination condition. A
                    Python identifier will be auto-generated for backend use.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    placeholder="Termination description"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Dynamic Configuration based on selected type */}
              {selectedConfigType && (
                <div className="space-y-4 border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 text-lg">
                    Configuration
                  </h4>

                  {selectedConfigType === "TextMentionTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="terminationText">Termination Text</Label>
                      <Input
                        id="terminationText"
                        value={formData.terminationText}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            terminationText: e.target.value,
                          })
                        }
                        placeholder="TASK_COMPLETE, STOP, ERROR, etc."
                      />
                      <p className="text-xs text-gray-500">
                        Enter the text that will trigger this termination
                        condition when mentioned by an agent.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "MaxMessageTermination" && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="maxMessages">Maximum Messages</Label>
                        <Input
                          id="maxMessages"
                          type="number"
                          value={formData.maxMessages}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              maxMessages: parseInt(e.target.value) || 10,
                            })
                          }
                          placeholder="10"
                          min="1"
                        />
                        <p className="text-xs text-gray-500">
                          Maximum number of messages before termination.
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="includeAgentEvent"
                          checked={formData.includeAgentEvent}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              includeAgentEvent: e.target.checked,
                            })
                          }
                          className="h-4 w-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <Label htmlFor="includeAgentEvent" className="text-sm">
                          Include Agent Events
                        </Label>
                      </div>
                    </div>
                  )}

                  {selectedConfigType === "TimeoutTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="timeout">Timeout (seconds)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={formData.timeout}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            timeout: parseInt(e.target.value) || 1000,
                          })
                        }
                        placeholder="1000"
                        min="1"
                      />
                      <p className="text-xs text-gray-500">
                        Timeout duration in seconds before termination.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "FunctionCallTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="functionName">Function Name</Label>
                      <Input
                        id="functionName"
                        value={formData.functionName}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            functionName: e.target.value,
                          })
                        }
                        placeholder="TERMINATE"
                      />
                      <p className="text-xs text-gray-500">
                        Function name that triggers termination when called.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "HandoffTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="target">Target Agent</Label>
                      <Input
                        id="target"
                        value={formData.target}
                        onChange={(e) =>
                          setFormData({ ...formData, target: e.target.value })
                        }
                        placeholder="assistant_agent"
                      />
                      <p className="text-xs text-gray-500">
                        Target agent for handoff termination.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "TokenUsageTermination" && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="maxTotalToken">
                          Maximum Total Tokens
                        </Label>
                        <Input
                          id="maxTotalToken"
                          type="number"
                          value={formData.maxTotalToken}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              maxTotalToken: parseInt(e.target.value) || 100,
                            })
                          }
                          placeholder="100"
                          min="1"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maxPromptToken">
                          Maximum Prompt Tokens
                        </Label>
                        <Input
                          id="maxPromptToken"
                          type="number"
                          value={formData.maxPromptToken}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              maxPromptToken: parseInt(e.target.value) || 100,
                            })
                          }
                          placeholder="100"
                          min="1"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maxCompletionToken">
                          Maximum Completion Tokens
                        </Label>
                        <Input
                          id="maxCompletionToken"
                          type="number"
                          value={formData.maxCompletionToken}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              maxCompletionToken:
                                parseInt(e.target.value) || 100,
                            })
                          }
                          placeholder="100"
                          min="1"
                        />
                      </div>
                      <p className="text-xs text-gray-500">
                        Terminate when token usage limits are reached.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "SourceMatchTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="sources">Sources (comma-separated)</Label>
                      <Input
                        id="sources"
                        value={formData.sources}
                        onChange={(e) =>
                          setFormData({ ...formData, sources: e.target.value })
                        }
                        placeholder="assistant_agent, user_agent"
                      />
                      <p className="text-xs text-gray-500">
                        Enter agent names separated by commas. Terminates when
                        any of these sources responds.
                      </p>
                    </div>
                  )}

                  {selectedConfigType === "TextMessageTermination" && (
                    <div className="space-y-2">
                      <Label htmlFor="source">Source Agent</Label>
                      <Input
                        id="source"
                        value={formData.source}
                        onChange={(e) =>
                          setFormData({ ...formData, source: e.target.value })
                        }
                        placeholder="assistant_agent"
                      />
                      <p className="text-xs text-gray-500">
                        Terminate when a text message is received from this
                        source.
                      </p>
                    </div>
                  )}

                  {(selectedConfigType === "StopMessageTermination" ||
                    selectedConfigType === "ExternalTermination") && (
                    <div className="p-4 bg-gray-50 rounded-lg border">
                      <p className="text-sm text-gray-600">
                        This termination condition requires no additional
                        configuration.
                      </p>
                      {selectedConfigType === "StopMessageTermination" && (
                        <p className="text-xs text-gray-500 mt-2">
                          Terminates when a StopMessage is received.
                        </p>
                      )}
                      {selectedConfigType === "ExternalTermination" && (
                        <p className="text-xs text-gray-500 mt-2">
                          Externally controlled termination condition.
                        </p>
                      )}
                    </div>
                  )}

                  {selectedConfigType === "OrTerminationCondition" && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm text-yellow-800 font-medium">
                        Complex Condition
                      </p>
                      <p className="text-xs text-yellow-700 mt-1">
                        OrTerminationCondition combines multiple conditions and
                        requires advanced configuration. This is not supported
                        in the UI yet.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Validation Section */}
              {validationResult && (
                <div className="space-y-4 border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 text-lg">
                    Validation Results
                  </h4>
                  <ValidationResults
                    validation={validationResult}
                    isValidating={validateComponentMutation.isPending}
                  />
                </div>
              )}

              {/* Additional Configuration Info */}
              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h4 className="font-medium text-gray-900 text-lg">
                  Configuration Guidelines
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex gap-3">
                    <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <div className="w-2 h-2 bg-teal-800 rounded-full"></div>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">
                        Case Sensitive
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Termination text matching is case-sensitive. Use
                        consistent casing across your agents.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">Exact Match</p>
                      <p className="text-xs text-gray-500 mt-1">
                        The termination text must appear exactly as specified in
                        the agent's output.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    </div>
                    <div>
                      <p className="font-medium text-gray-700">
                        Best Practices
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Use clear, unique termination keywords that won't
                        accidentally trigger during normal conversation.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 border-t border-gray-200 bg-gray-50 p-6">
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={
                createMutation.isPending ||
                updateMutation.isPending ||
                validateComponentMutation.isPending
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="bg-teal-800 hover:bg-teal-700"
              disabled={
                createMutation.isPending ||
                updateMutation.isPending ||
                validateComponentMutation.isPending ||
                !formData.label.trim()
              }
            >
              {createMutation.isPending || updateMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : validationResult?.is_valid ? (
                <CheckCircle2 className="h-4 w-4 mr-2 text-green-200" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {createMutation.isPending || updateMutation.isPending
                ? "Saving..."
                : isEditing
                  ? "Update Configuration"
                  : "Create Configuration"}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
