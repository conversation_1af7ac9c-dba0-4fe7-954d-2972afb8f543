import { useState, useEffect } from "react";
import { X, Save, Loader2, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PythonCodeEditor } from "@/components/PythonCodeEditor";
import { Tool2, ToolConfig } from "@/types/tool";
import { useTool, useCreateTool, useUpdateTool } from "@/hooks/useTools";
import { useValidateComponent } from "@/hooks/useValidation";
import { ValidationResults } from "@/components/ui/validation-results";
import { ComponentModel, ValidationResponse } from "@/types/validation";
import { makeIdentifier } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";

interface ToolConfigurationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  tool: Tool2 | null;
  toolToFork?: Tool2 | null; // Optional prop for forking default tools
}

export const ToolConfigurationDrawer = ({
  isOpen,
  onClose,
  tool,
  toolToFork,
}: ToolConfigurationDrawerProps) => {
  const isEditMode = !!tool;
  const isForkMode = !!toolToFork;

  // Fetch full tool data if editing
  const { data: fullToolData, isLoading: isLoadingTool } = useTool(
    tool?.id || "",
  );

  // Fetch full tool data for forking if needed
  const { data: forkToolData, isLoading: isLoadingForkTool } = useTool(
    toolToFork?.id || "",
  );

  // Mutations
  const createToolMutation = useCreateTool();
  const updateToolMutation = useUpdateTool();
  const validateComponentMutation = useValidateComponent();

  // Validation state
  const [validationResult, setValidationResult] =
    useState<ValidationResponse | null>(null);

  const [formData, setFormData] = useState({
    label: "",
    description: "",
    provider: "autogen_core.tools.FunctionTool",
    globalImports: "requests, bs4",
    sourceCode: `def web_scrape(url: str) -> dict:
    """
    Scrape content from a web page

    Args:
        url: The URL to scrape

    Returns:
        dict: Scraped content and metadata
    """
    try:
        response = requests.get(url)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        return {
            "title": soup.title.string if soup.title else "",
            "content": soup.get_text().strip(),
            "status": "success"
        }
    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }`,
    hasCancellationSupport: false,
  });

  // Initialize form data when tool or full data loads
  useEffect(() => {
    if (tool && isEditMode && fullToolData) {
      // Populate form with existing tool data from full backend response
      const component = fullToolData.component;
      const config = component?.config;

      setFormData({
        label: component?.label || "",
        description: config?.description || "",
        provider: component?.provider || "autogen_core.tools.FunctionTool",
        globalImports: config?.global_imports?.join(", ") || "",
        sourceCode: config?.source_code || "",
        hasCancellationSupport: config?.has_cancellation_support || false,
      });
    } else if (toolToFork && isForkMode && forkToolData) {
      // Populate form with forked tool data (create new tool based on default)
      const component = forkToolData.component;
      const config = component?.config;

      setFormData({
        label: `${component?.label || "Forked Tool"} (Custom)`, // Add suffix to indicate it's a fork
        description: config?.description || "",
        provider: component?.provider || "autogen_core.tools.FunctionTool",
        globalImports: config?.global_imports?.join(", ") || "",
        sourceCode: config?.source_code || "",
        hasCancellationSupport: config?.has_cancellation_support || false,
      });
    } else if (!isEditMode && !isForkMode) {
      // Reset form for new tool
      setFormData({
        label: "",
        description: "",
        provider: "autogen_core.tools.FunctionTool",
        globalImports: "requests, bs4",
        sourceCode: `def web_scrape(url: str) -> dict:
    """
    Scrape content from a web page

    Args:
        url: The URL to scrape

    Returns:
        dict: Scraped content and metadata
    """
    try:
        response = requests.get(url)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        return {
            "title": soup.title.string if soup.title else "",
            "content": soup.get_text().strip(),
            "status": "success"
        }
    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }`,
        hasCancellationSupport: false,
      });
    }
  }, [
    tool,
    isEditMode,
    fullToolData,
    toolToFork,
    isForkMode,
    forkToolData,
    isOpen,
  ]);

  // Clear validation when form data changes
  useEffect(() => {
    setValidationResult(null);
  }, [
    formData.label,
    formData.description,
    formData.provider,
    formData.globalImports,
    formData.sourceCode,
    formData.hasCancellationSupport,
  ]);

  const createToolComponent = (): ComponentModel => {
    return {
      provider: formData.provider,
      component_type: "tool",
      version: 1,
      component_version: 1,
      description: "Create custom tools by wrapping standard Python functions.",
      label: formData.label,
      config: {
        name: makeIdentifier(formData.label), // Auto-generate Python identifier
        description: formData.description,
        global_imports: formData.globalImports
          .split(",")
          .map((s) => s.trim())
          .filter(Boolean),
        source_code: formData.sourceCode,
        has_cancellation_support: formData.hasCancellationSupport,
      },
    };
  };

  const handleSave = async () => {
    if (!formData.label.trim()) {
      toast({
        title: "Validation Error",
        description: "Tool label is required",
        variant: "destructive",
      });
      return;
    }

    try {
      const component = createToolComponent();

      // Validate before saving
      const validationResult =
        await validateComponentMutation.mutateAsync(component);
      setValidationResult(validationResult);

      if (!validationResult.is_valid) {
        toast({
          title: "Validation Failed",
          description:
            validationResult.errors.map((error) => error.error).join("\n") +
            "\n\nPlease fix the validation errors before saving",
          variant: "destructive",
        });
        return;
      }

      const toolConfig: ToolConfig = {
        component_type: "tool",
        component_version: 1,
        config: {
          name: makeIdentifier(formData.label), // Auto-generate Python identifier
          description: formData.description,
          global_imports: formData.globalImports
            .split(",")
            .map((s) => s.trim())
            .filter(Boolean),
          source_code: formData.sourceCode,
          has_cancellation_support: formData.hasCancellationSupport,
        },
        description:
          component.description ||
          "Create custom tools by wrapping standard Python functions.",
        label: component.label,
        provider: component.provider,
        version: component.version || 1,
      };

      if (isEditMode && tool?.id && !isForkMode) {
        // Update existing tool (only if not forking)
        await updateToolMutation.mutateAsync({
          id: tool.id,
          data: { component: toolConfig },
        });
        toast({
          title: "Success",
          description: "Tool updated successfully",
        });
      } else {
        // Create new tool (for new tools or when forking)
        await createToolMutation.mutateAsync({
          component: toolConfig,
        });
        toast({
          title: "Success",
          description: isForkMode
            ? "Tool forked successfully"
            : "Tool created successfully",
        });
      }

      onClose();
    } catch (error: any) {
      console.error("Error saving tool:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to save tool",
        variant: "destructive",
      });
    }
  };

  const isLoading =
    isLoadingTool ||
    isLoadingForkTool ||
    createToolMutation.isPending ||
    updateToolMutation.isPending ||
    validateComponentMutation.isPending;

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />

      <div className="fixed right-0 top-0 h-full w-1/2 min-w-[600px] max-w-[900px] bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out flex flex-col">
        <div className="flex-shrink-0 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between p-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {isForkMode
                  ? "Fork Tool"
                  : isEditMode
                    ? "Tool Configuration"
                    : "Add New Tool"}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {isForkMode
                  ? "Create a custom version based on the default tool"
                  : isEditMode
                    ? "Configure tool settings and behavior"
                    : "Create a new tool"}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-10 w-10 p-0"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6">
              {/* Component Details */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 text-lg">
                  Component Details
                </h4>

                <div className="space-y-2">
                  <Label htmlFor="label">
                    Label <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="label"
                    value={formData.label}
                    onChange={(e) =>
                      setFormData({ ...formData, label: e.target.value })
                    }
                    placeholder="Tool label (e.g., Web Scraper, Email Sender)"
                  />
                  <p className="text-xs text-gray-500">
                    This is the display name for your tool. A Python identifier
                    will be auto-generated for backend use.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    placeholder="Tool description"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Configuration */}
              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h4 className="font-medium text-gray-900 text-lg">
                  Configuration
                </h4>

                <div className="space-y-2">
                  <Label htmlFor="provider">Provider</Label>
                  <Input
                    id="provider"
                    value={formData.provider}
                    onChange={(e) =>
                      setFormData({ ...formData, provider: e.target.value })
                    }
                    placeholder="Tool provider"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="globalImports">
                    Global Imports (comma separated)
                  </Label>
                  <Input
                    id="globalImports"
                    value={formData.globalImports}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        globalImports: e.target.value,
                      })
                    }
                    placeholder="requests, bs4, pandas"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sourceCode">Source Code</Label>
                  <PythonCodeEditor
                    value={formData.sourceCode}
                    onChange={(value) =>
                      setFormData({ ...formData, sourceCode: value })
                    }
                    height="400px"
                    placeholder={`def web_scrape(url: str) -> dict:
    """
    Scrape content from a web page

    Args:
        url: The URL to scrape

    Returns:
        dict: Scraped content and metadata
    """
    try:
        response = requests.get(url)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        return {
            "title": soup.title.string if soup.title else "",
            "content": soup.get_text().strip(),
            "status": "success"
        }
    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }`}
                  />
                </div>
              </div>

              {/* Validation Section */}
              {validationResult && (
                <div className="space-y-4 border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 text-lg">
                    Validation Results
                  </h4>
                  <ValidationResults
                    validation={validationResult}
                    isValidating={validateComponentMutation.isPending}
                  />
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 border-t border-gray-200 bg-gray-50 p-6">
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !formData.label.trim()}
              className="bg-teal-800 hover:bg-teal-700"
            >
              {createToolMutation.isPending || updateToolMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : validationResult?.is_valid ? (
                <CheckCircle2 className="h-4 w-4 mr-2 text-green-200" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {createToolMutation.isPending || updateToolMutation.isPending
                ? "Saving..."
                : isEditMode
                  ? "Update Tool"
                  : "Create Tool"}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
