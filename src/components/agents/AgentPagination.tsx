import { ChevronLeft, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

interface AgentPaginationProps {
  currentPage: number;
  totalPages: number;
  totalAgents: number;
  onPageChange: (page: number) => void;
  isPaginating: boolean;
}

export const AgentPagination: React.FC<AgentPaginationProps> = ({
  currentPage,
  totalPages,
  totalAgents,
  onPageChange,
  isPaginating,
}) => {
  // Don't show pagination if there's only one page or no data
  if (totalPages <= 1) {
    return null;
  }

  // Show skeleton during pagination loading
  if (isPaginating) {
    return (
      <div className="flex items-center justify-between mt-8">
        <Skeleton className="h-4 w-32" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-20" />
        </div>
        <Skeleton className="h-4 w-24" />
      </div>
    );
  }

  const startItem = (currentPage - 1) * 9 + 1;
  const endItem = Math.min(currentPage * 9, totalAgents);

  // Generate page numbers to show
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, start + maxVisiblePages - 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="flex items-center justify-between mt-8">
      <div className="text-sm text-slate-600">
        {/* Showing {startItem} to {endItem} of {totalAgents} agents */}
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="gap-1"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>

        {pageNumbers.map((pageNum) => (
          <Button
            key={pageNum}
            variant={pageNum === currentPage ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(pageNum)}
            className="w-10 h-10 p-0"
          >
            {pageNum}
          </Button>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="gap-1"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="text-sm text-slate-600">
        Page {currentPage} of {totalPages}
      </div>
    </div>
  );
};
