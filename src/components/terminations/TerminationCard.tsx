import { More<PERSON><PERSON><PERSON>, Trash2, Edit, GitFork } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Termination2 } from "@/types/termination";

interface TerminationCardProps {
  termination: Termination2;
  onClick: (termination: Termination2) => void;
  onDelete: (terminationId: string) => void;
  onFork?: (termination: Termination2) => void; // New prop for forking default termination conditions
  isDeleting?: boolean;
}

export const TerminationCard: React.FC<TerminationCardProps> = ({
  termination,
  onClick,
  onDelete,
  onFork,
  isDeleting = false,
}) => {
  const handleCardClick = () => {
    // Only allow clicking for non-default termination conditions
    if (!termination.is_default) {
      onClick(termination);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(termination.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(termination);
  };

  const handleFork = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFork) {
      onFork(termination);
    }
  };

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-300 bg-white border-slate-200 border-2 overflow-hidden group h-[240px] ${
        termination.is_default ? "cursor-default" : "cursor-pointer"
      }`}
      onClick={handleCardClick}
    >
      <CardContent className="p-6 flex flex-col h-full justify-between">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex flex-col gap-2 max-w-[85%]">
              <h3 className="text-lg font-bold text-slate-900 transition-colors leading-tight truncate">
                {termination.name}
              </h3>
              {termination.is_default && (
                <Badge
                  variant="secondary"
                  className="w-fit bg-blue-50 text-blue-700 border-blue-200 text-xs"
                >
                  Default
                </Badge>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
              {termination.description || "No description available"}
            </p>

          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="text-xs text-slate-400">
            Modified {termination.lastModified}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-slate-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {termination.is_default ? (
                // For default termination conditions: show Fork & Edit option
                <DropdownMenuItem onClick={handleFork}>
                  <GitFork className="h-4 w-4 mr-2" />
                  Fork & Edit
                </DropdownMenuItem>
              ) : (
                // For non-default termination conditions: show Edit and Delete options
                <>
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {isDeleting ? "Deleting..." : "Delete"}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};
