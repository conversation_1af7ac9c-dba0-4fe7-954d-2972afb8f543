import React from "react";

interface TerminationPaginationProps {
  currentPage: number;
  totalPages: number;
  totalTerminations: number;
  onPageChange: (page: number) => void;
  isPaginating: boolean;
}

export const TerminationPagination: React.FC<TerminationPaginationProps> = ({
  currentPage,
  totalPages,
  totalTerminations,
  onPageChange,
  isPaginating,
}) => {
  if (totalPages <= 1) return null;
  return (
    <div className="flex items-center gap-2 mt-8">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || isPaginating}
      >
        Prev
      </button>
      <span>
        Page {currentPage} of {totalPages}
      </span>
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages || isPaginating}
      >
        Next
      </button>
    </div>
  );
};
