import { <PERSON><PERSON>ey } from "@/types/api-key";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rig<PERSON>,
} from "@/components/ui/tooltip";

import { toTitleCase } from "@/lib/utils";

function formatScopes(
  scopes: string[]
): Record<"read_write" | "read" | "write", string[]> {
  const permissions = {
    read_write: [],
    read: [],
    write: [],
  };

  const scopes_map = new Map<string, boolean>();
  scopes.forEach((scope) => {
    scopes_map.set(scope, true);
  });

  scopes.forEach((scope) => {
    const scope_target = scope.split(":").at(-1);
    const read_scope = `read:${scope_target}`;
    const write_scope = `write:${scope_target}`;

    if (scopes_map.has(read_scope) && scopes_map.has(write_scope)) {
      permissions.read_write.push(toTitleCase(scope_target));
      scopes_map.delete(read_scope);
      scopes_map.delete(write_scope);
    } else if (scopes_map.has(read_scope)) {
      permissions.read.push(toTitleCase(scope_target));
      scopes_map.delete(read_scope);
    } else if (scopes_map.has(write_scope)) {
      permissions.write.push(toTitleCase(scope_target));
      scopes_map.delete(write_scope);
    }
  });

  return permissions;
}

export default function ScopesTooltip({
  api_key,
  children,
}: {
  api_key: APIKey;
  children: React.ReactNode;
}) {
  const permissions = formatScopes(api_key.scopes);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent className="max-w-xs">
          <div className="flex flex-col gap-2">
            {permissions.read_write.length > 0 && (
              <div>
                <h4 className="font-medium text-xs leading-5">
                  READ AND WRITE
                </h4>
                <p className="text-xs leading-5 text-muted-foreground">
                  {permissions.read_write.join(", ")}
                </p>
              </div>
            )}
            {permissions.read.length > 0 && (
              <div>
                <h4 className="font-medium text-xs leading-5">READ</h4>
                <p className="text-xs leading-5 text-muted-foreground">
                  {permissions.read.join(", ")}
                </p>
              </div>
            )}
            {permissions.write.length > 0 && (
              <div>
                <h4 className="font-medium text-xs leading-5">WRITE</h4>
                <p className="text-xs leading-5 text-muted-foreground">
                  {permissions.write.join(", ")}
                </p>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
