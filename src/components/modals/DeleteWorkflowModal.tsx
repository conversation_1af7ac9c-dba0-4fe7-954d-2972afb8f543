import { useState, useEffect } from "react";
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Team2 } from "@/types/team";

interface DeleteWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  workflow: Team2 | null;
  onDelete: (teamId: string, teamName: string) => Promise<void>;
}

export const DeleteWorkflowModal = ({
  isOpen,
  onClose,
  workflow,
  onDelete,
}: DeleteWorkflowModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  // Reset deleting state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsDeleting(false);
    }
  }, [isOpen]);

  const handleDelete = async () => {
    if (!workflow) return;

    setIsDeleting(true);
    try {
      await onDelete(workflow.id, workflow.name);
      onClose();
      toast({
        title: "Workflow deleted",
        description: `Workflow "${workflow.name}" has been successfully deleted.`,
      });
    } catch (error: unknown) {
      console.error("Failed to delete workflow:", error);

      // Handle specific error cases
      let errorMessage = "Failed to delete workflow. Please try again.";

      if (error instanceof Error) {
        const message = error.message;
        if (message.includes("deployed") || message.includes("active")) {
          errorMessage =
            "Cannot delete a deployed workflow. Please undeploy it first before deletion.";
        } else if (
          message.includes("permission") ||
          message.includes("unauthorized")
        ) {
          errorMessage = "You don't have permission to delete this workflow.";
        } else if (
          message.includes("in use") ||
          message.includes("referenced")
        ) {
          errorMessage =
            "This workflow is currently being used and cannot be deleted.";
        } else {
          errorMessage = message;
        }
      }

      toast({
        title: "Deletion failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!workflow) return null;

  const isDeployed = workflow.status === "Active";

  return (
    <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="h-5 w-5 text-red-600" />
            </div>
            <AlertDialogTitle className="text-xl">
              Delete Workflow
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-4">
            <p className="text-slate-600">
              Are you sure you want to delete this workflow? This action cannot
              be undone.
            </p>

            {isDeployed && (
              <div className="flex items-start gap-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-amber-800 mb-1">Warning</p>
                  <p className="text-amber-700">
                    This workflow is currently deployed. You may need to
                    undeploy it first before deletion.
                  </p>
                </div>
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="flex gap-2">
          <AlertDialogCancel
            disabled={isDeleting}
            className="border-slate-300 text-slate-600 hover:bg-slate-50"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Workflow
                </>
              )}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
