import { useRef } from "react";
import Editor from "@monaco-editor/react";
import type { editor } from "monaco-editor";

interface PythonCodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  readOnly?: boolean;
  className?: string;
}

export const PythonCodeEditor = ({
  value,
  onChange,
  placeholder = "# Write your Python function here...",
  height = "400px",
  readOnly = false,
  className = "",
}: PythonCodeEditorProps) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);

  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;

    // Set placeholder if value is empty
    if (!value && placeholder) {
      editor.setValue(placeholder);
      // Select all placeholder text so user can start typing immediately
      editor.setSelection(editor.getModel()?.getFullModelRange()!);
    }
  };

  const handleEditorChange = (newValue: string | undefined) => {
    if (newValue !== undefined) {
      onChange(newValue);
    }
  };

  return (
    <div className={`border rounded-md overflow-hidden bg-white ${className}`}>
      <Editor
        height={height}
        defaultLanguage="python"
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        theme="vs-light"
        options={{
          minimap: { enabled: false },
          scrollBeyondLastLine: true,
          wordWrap: "on",
          fontSize: 14,
          lineNumbers: "on",
          roundedSelection: false,
          automaticLayout: true,
          readOnly,
          padding: { top: 16, bottom: 16 },
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnCommitCharacter: true,
          acceptSuggestionOnEnter: "on",
          quickSuggestions: true,
          parameterHints: { enabled: true },
          formatOnPaste: true,
          formatOnType: true,
          tabSize: 4,
          insertSpaces: true,
          detectIndentation: false,
        }}
      />
    </div>
  );
};
