import type { SVGProps } from "react";

export default function PDF(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="45"
      height="45"
      viewBox="0 0 45 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M28.1257 3.75H11.2507C10.2562 3.75 9.30234 4.14509 8.59908 4.84835C7.89582 5.55161 7.50073 6.50544 7.50073 7.5V37.5C7.50073 38.4946 7.89582 39.4484 8.59908 40.1516C9.30234 40.8549 10.2562 41.25 11.2507 41.25H33.7507C34.7453 41.25 35.6991 40.8549 36.4024 40.1516C37.1056 39.4484 37.5007 38.4946 37.5007 37.5V13.125L28.1257 3.75Z"
        fill="white"
        stroke="#EA4335"
        strokeOpacity="0.5"
        strokeWidth="0.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.2505 3.75V11.25C26.2505 12.2446 26.6456 13.1984 27.3488 13.9017C28.0521 14.6049 29.0059 15 30.0005 15H37.5005"
        fill="white"
      />
      <path
        d="M26.2505 3.75V11.25C26.2505 12.2446 26.6456 13.1984 27.3488 13.9017C28.0521 14.6049 29.0059 15 30.0005 15H37.5005"
        stroke="#EA4335"
        strokeOpacity="0.5"
        strokeWidth="0.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.3258 26.7818V21.6909H18.0459C18.4453 21.6909 18.7718 21.763 19.0253 21.9072C19.2806 22.0497 19.4695 22.2427 19.5921 22.4863C19.7147 22.73 19.7761 23.0017 19.7761 23.3017C19.7761 23.6016 19.7147 23.8742 19.5921 24.1195C19.4711 24.3648 19.2839 24.5603 19.0303 24.7062C18.7768 24.8503 18.452 24.9224 18.0559 24.9224H16.8229V24.3755H18.036C18.3094 24.3755 18.529 24.3283 18.6947 24.2339C18.8605 24.1394 18.9806 24.0118 19.0552 23.851C19.1314 23.6886 19.1695 23.5055 19.1695 23.3017C19.1695 23.0979 19.1314 22.9156 19.0552 22.7548C18.9806 22.5941 18.8596 22.4681 18.6923 22.377C18.5249 22.2842 18.3028 22.2378 18.0261 22.2378H16.9423V26.7818H16.3258ZM22.3402 26.7818H20.7691V21.6909H22.4098C22.9036 21.6909 23.3262 21.7928 23.6775 21.9966C24.0288 22.1988 24.2981 22.4897 24.4854 22.8692C24.6727 23.247 24.7663 23.6994 24.7663 24.2264C24.7663 24.7567 24.6718 25.2133 24.4829 25.5961C24.294 25.9772 24.0189 26.2706 23.6576 26.476C23.2964 26.6799 22.8572 26.7818 22.3402 26.7818ZM21.3856 26.2349H22.3004C22.7213 26.2349 23.0702 26.1537 23.3469 25.9913C23.6237 25.8289 23.83 25.5977 23.9659 25.2978C24.1018 24.9978 24.1697 24.6407 24.1697 24.2264C24.1697 23.8154 24.1026 23.4616 23.9684 23.165C23.8341 22.8667 23.6336 22.638 23.3668 22.4789C23.1 22.3181 22.7677 22.2378 22.37 22.2378H21.3856V26.2349ZM25.8004 26.7818V21.6909H28.8529V22.2378H26.4169V23.9579H28.6242V24.5048H26.4169V26.7818H25.8004Z"
        fill="#EA4335"
      />
    </svg>
  );
}
