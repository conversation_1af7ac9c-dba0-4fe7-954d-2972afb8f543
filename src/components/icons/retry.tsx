import type { SVGProps } from "react";

export default function Retry(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.50846 6.0653L13.9359 6.01769L13.8823 1.58728M13.3319 6.02959C13.0116 4.832 12.3125 3.77013 11.339 3.00252C10.3656 2.23491 9.16998 1.80274 7.93071 1.77053C6.69144 1.73833 5.47499 2.10781 4.46298 2.82382C3.45097 3.53982 2.69769 4.56395 2.3156 5.74329C1.93351 6.92262 1.94311 8.19391 2.34297 9.36734C2.74282 10.5408 3.51149 11.5534 4.5342 12.254C5.5569 12.9547 6.77879 13.3058 8.01744 13.2548C9.25608 13.2039 10.445 12.7537 11.4068 11.9715"
        stroke="#666F8D"
        strokeWidth="1.1375"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
