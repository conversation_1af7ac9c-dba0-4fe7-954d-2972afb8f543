import type { SVGProps } from "react";

export default function Star(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={"14"}
      height={"14"}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1_1101)">
        <path
          d="M5.56534 3.80675C5.88926 2.85884 7.19901 2.83013 7.58305 3.72063L7.61555 3.8073L8.05267 5.08563C8.15285 5.3788 8.31473 5.64707 8.52741 5.87236C8.74008 6.09764 8.99859 6.2747 9.28551 6.39159L9.40305 6.43546L10.6814 6.87205C11.6293 7.19596 11.658 8.50571 10.768 8.88975L10.6814 8.92225L9.40305 9.35938C9.10978 9.45949 8.8414 9.62134 8.61601 9.83402C8.39063 10.0467 8.21349 10.3052 8.09655 10.5922L8.05267 10.7092L7.61609 11.9881C7.29217 12.936 5.98242 12.9647 5.59892 12.0748L5.56534 11.9881L5.12876 10.7098C5.02864 10.4165 4.86679 10.1481 4.65412 9.92272C4.44144 9.69734 4.18289 9.5202 3.89592 9.40325L3.77892 9.35938L2.50059 8.92279C1.55213 8.59888 1.52342 7.28913 2.41392 6.90563L2.50059 6.87205L3.77892 6.43546C4.07209 6.33528 4.34037 6.1734 4.56565 5.96073C4.79094 5.74806 4.968 5.48954 5.08488 5.20263L5.12876 5.08563L5.56534 3.80675ZM10.924 1.93909C11.0254 1.93909 11.1247 1.96751 11.2107 2.02113C11.2967 2.07476 11.3659 2.15142 11.4105 2.24242L11.4365 2.3058L11.626 2.86155L12.1823 3.05113C12.2839 3.08563 12.3729 3.1495 12.4381 3.23465C12.5033 3.3198 12.5418 3.4224 12.5487 3.52943C12.5555 3.63647 12.5304 3.74312 12.4766 3.83589C12.4227 3.92865 12.3426 4.00335 12.2463 4.0505L12.1823 4.0765L11.6266 4.26609L11.437 4.82238C11.4024 4.9239 11.3385 5.01288 11.2533 5.07804C11.1682 5.1432 11.0655 5.18161 10.9585 5.18839C10.8515 5.19518 10.7449 5.17004 10.6521 5.11617C10.5594 5.06229 10.4847 4.9821 10.4376 4.88575L10.4116 4.82238L10.222 4.26663L9.66576 4.07705C9.5642 4.04254 9.47518 3.97867 9.40996 3.89352C9.34475 3.80837 9.30628 3.70578 9.29944 3.59874C9.29259 3.49171 9.31768 3.38505 9.37151 3.29229C9.42535 3.19952 9.50551 3.12483 9.60184 3.07767L9.66576 3.05167L10.2215 2.86209L10.4111 2.3058C10.4476 2.19878 10.5167 2.10587 10.6087 2.04011C10.7007 1.97434 10.811 1.93902 10.924 1.93909Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_1101">
          <rect
            width="13"
            height="13"
            fill="white"
            transform="translate(0.632324 0.855835)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
