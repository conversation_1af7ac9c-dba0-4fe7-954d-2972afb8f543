export default function Wand(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M23.625 1.75C23.8571 1.75 24.0797 1.84219 24.2437 2.00628C24.4078 2.17038 24.5 2.39294 24.5 2.625V3.5H25.375C25.6071 3.5 25.8297 3.59219 25.9937 3.75628C26.1578 3.92038 26.25 4.14294 26.25 4.375C26.25 4.60706 26.1578 4.82962 25.9937 4.99372C25.8297 5.15781 25.6071 5.25 25.375 5.25H24.5V6.125C24.5 6.35706 24.4078 6.57962 24.2437 6.74372C24.0797 6.90781 23.8571 7 23.625 7C23.393 7 23.1704 6.90781 23.0063 6.74372C22.8422 6.57962 22.75 6.35706 22.75 6.125V5.25H21.875C21.643 5.25 21.4204 5.15781 21.2563 4.99372C21.0922 4.82962 21 4.60706 21 4.375C21 4.14294 21.0922 3.92038 21.2563 3.75628C21.4204 3.59219 21.643 3.5 21.875 3.5H22.75V2.625C22.75 2.39294 22.8422 2.17038 23.0063 2.00628C23.1704 1.84219 23.393 1.75 23.625 1.75ZM6.12503 5.25C6.35709 5.25 6.57965 5.34219 6.74374 5.50628C6.90784 5.67038 7.00003 5.89294 7.00003 6.125V7H7.87503C8.10709 7 8.32965 7.09219 8.49374 7.25628C8.65784 7.42038 8.75003 7.64294 8.75003 7.875C8.75003 8.10706 8.65784 8.32962 8.49374 8.49372C8.32965 8.65781 8.10709 8.75 7.87503 8.75H7.00003V9.625C7.00003 9.85706 6.90784 10.0796 6.74374 10.2437C6.57965 10.4078 6.35709 10.5 6.12503 10.5C5.89296 10.5 5.6704 10.4078 5.50631 10.2437C5.34221 10.0796 5.25003 9.85706 5.25003 9.625V8.75H4.37503C4.14296 8.75 3.9204 8.65781 3.75631 8.49372C3.59221 8.32962 3.50003 8.10706 3.50003 7.875C3.50003 7.64294 3.59221 7.42038 3.75631 7.25628C3.9204 7.09219 4.14296 7 4.37503 7H5.25003V6.125C5.25003 5.89294 5.34221 5.67038 5.50631 5.50628C5.6704 5.34219 5.89296 5.25 6.12503 5.25ZM21.875 21C22.1071 21 22.3297 20.9078 22.4937 20.7437C22.6578 20.5796 22.75 20.3571 22.75 20.125C22.75 19.8929 22.6578 19.6704 22.4937 19.5063C22.3297 19.3422 22.1071 19.25 21.875 19.25H21V18.375C21 18.1429 20.9078 17.9204 20.7437 17.7563C20.5797 17.5922 20.3571 17.5 20.125 17.5C19.893 17.5 19.6704 17.5922 19.5063 17.7563C19.3422 17.9204 19.25 18.1429 19.25 18.375V19.25H18.375C18.143 19.25 17.9204 19.3422 17.7563 19.5063C17.5922 19.6704 17.5 19.8929 17.5 20.125C17.5 20.3571 17.5922 20.5796 17.7563 20.7437C17.9204 20.9078 18.143 21 18.375 21H19.25V21.875C19.25 22.1071 19.3422 22.3296 19.5063 22.4937C19.6704 22.6578 19.893 22.75 20.125 22.75C20.3571 22.75 20.5797 22.6578 20.7437 22.4937C20.9078 22.3296 21 22.1071 21 21.875V21H21.875ZM15.2775 7.98525C15.906 7.35705 16.7582 7.00423 17.6468 7.00439C18.5353 7.00455 19.3875 7.35769 20.0157 7.98612C20.6439 8.61455 20.9967 9.4668 20.9965 10.3554C20.9963 11.2439 20.6432 12.0961 20.0148 12.7242L7.46728 25.2717C6.83885 25.8999 5.9866 26.2528 5.09803 26.2526C4.20946 26.2524 3.35735 25.8993 2.72915 25.2709C2.10095 24.6424 1.74813 23.7902 1.74829 22.9016C1.74846 22.0131 2.1016 21.1609 2.73003 20.5327L15.2775 7.98525ZM14.4375 11.2997L3.96728 21.77C3.66722 22.0701 3.49865 22.477 3.49865 22.9014C3.49865 23.3257 3.66722 23.7327 3.96728 24.0327C4.26734 24.3328 4.6743 24.5014 5.09865 24.5014C5.523 24.5014 5.92997 24.3328 6.23003 24.0327L16.7003 13.5625L14.4375 11.2997ZM17.9375 12.3252L18.7775 11.4852C18.9289 11.3373 19.0494 11.1608 19.1321 10.966C19.2147 10.7711 19.2579 10.5618 19.259 10.3502C19.2602 10.1385 19.2194 9.92878 19.1389 9.73303C19.0584 9.53729 18.9399 9.35946 18.7902 9.20986C18.6404 9.06026 18.4625 8.94186 18.2667 8.86153C18.0709 8.78119 17.8611 8.74051 17.6495 8.74185C17.4378 8.74319 17.2285 8.78651 17.0338 8.86932C16.839 8.95213 16.6626 9.07277 16.5148 9.22425L15.6748 10.0642L17.9375 12.3252Z"
        fill="black"
      />
    </svg>
  );
}
