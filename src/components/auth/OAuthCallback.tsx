import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { AuthLoadingSpinner } from "./AuthLoadingSpinner";

export const OAuthCallback: React.FC = () => {
  const { handleOAuthCallback } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);
  const pathName = window.location.pathname;
  const provider = pathName.includes("google") ? "google" : pathName.includes("github") ? "github" : null;

  const processOAuthCallback = async () => {
    try {
      // Get the URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get("code");
      const state = urlParams.get("state");

      // Extract provider from pathname

      if (!code || !state) {
        throw new Error("Missing required OAuth parameters");
      }

      if (!provider) {
        throw new Error("OAuth provider information is missing");
      }

      // Process the OAuth callback with provider
      await handleOAuthCallback(provider, code, state);

      toast({
        title: "Authentication Successful",
        description: `You have been successfully logged in!`,
      });

      // Force a page refresh to reinitialize the app with new auth state
      navigate("/");

    } catch (error) {

      setIsProcessing(false);
      setError(error instanceof Error ? error.message : "Authentication failed");

      toast({
        title: "Authentication Failed",
        description: error instanceof Error ? error.message : "Authentication failed",
        variant: "destructive",
      });

      const sourcePage = localStorage.getItem("oauth_source_page") || "login";
      navigate(`/${sourcePage}`, { replace: true });
      localStorage.removeItem("oauth_source_page");
    }
  };


  useEffect(() => {
    if (provider) {
      processOAuthCallback();
    }

  }, [provider]);

  if (isProcessing) {
    return <AuthLoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Authentication Failed</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            className="px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700"
            onClick={() => navigate("/login")}
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  return <AuthLoadingSpinner />;
};