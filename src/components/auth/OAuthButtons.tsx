import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const OAuthButtons: React.FC = () => {
  const { initiateOAuth } = useAuth();
  const { toast } = useToast();
  const location = useLocation();

  const handleOAuthLogin = async (provider: "google" | "github") => {
    try {
      // Get authorization URL from backend
      const authUrl = await initiateOAuth(provider);
      
      // Store whether user started from login or register page
      const sourcePage = location.pathname === "/register" ? "register" : "login";
      localStorage.setItem("oauth_source_page", sourcePage);
      
      // Redirect to the authorization URL in the same window
      window.location.href = authUrl;
    } catch (error) {
      toast({
        title: "Authentication Failed",
        description:
          error instanceof Error
            ? error.message
            : `Failed to authenticate with ${provider}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-3">
      <Button
        type="button"
        variant="outline"
        className="w-full h-11 border-gray-300 hover:bg-gray-50 text-gray-700"
        onClick={() => handleOAuthLogin("google")}
      >
        <img src="/google.svg" alt="Google" className="h-5 w-5 mr-2" />
        Continue with Google
      </Button>

      <Button
        type="button"
        variant="outline"
        className="w-full h-11 border-gray-300 hover:bg-gray-50 text-gray-700"
        onClick={() => handleOAuthLogin("github")}
      >
        <img src="/github.svg" alt="Github" className="h-5 w-5 mr-2" />
        Continue with GitHub
      </Button>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-2 text-gray-500">Or</span>
        </div>
      </div>
    </div>
  );
};
