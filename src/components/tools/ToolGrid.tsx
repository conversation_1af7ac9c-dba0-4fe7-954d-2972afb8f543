import { ToolCard } from "./ToolCard";
import { SkeletonGrid } from "@/components/ui/skeleton-card";
import { Tool2 } from "@/types/tool";

interface ToolGridProps {
  tools: Tool2[];
  isLoading: boolean;
  isSearching: boolean;
  isPaginating: boolean;
  searchTerm: string;
  onToolClick: (tool: Tool2) => void;
  onToolDelete: (toolId: string) => void;
  onToolFork?: (tool: Tool2) => void; // New prop for forking default tools
  deletingToolId?: string;
}

export const ToolGrid: React.FC<ToolGridProps> = ({
  tools,
  isLoading,
  isSearching,
  isPaginating,
  searchTerm,
  onToolClick,
  onToolDelete,
  onToolFork,
  deletingToolId,
}) => {
  // Show skeleton loading during initial load, search, or pagination
  if (isLoading || isSearching || isPaginating) {
    return <SkeletonGrid count={9} />;
  }

  // Show empty state when no tools found
  if (tools.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-6">
          <svg
            className="w-12 h-12 text-slate-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-slate-900 mb-2">
          {searchTerm.trim() ? "No tools found" : "No tools yet"}
        </h3>
        <p className="text-slate-600 max-w-md">
          {searchTerm.trim()
            ? `No tools match "${searchTerm}". Try adjusting your search terms.`
            : "Get started by creating your first AI tool to help automate your workflows."}
        </p>
      </div>
    );
  }

  // Show tools grid
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {tools.map((tool) => (
        <ToolCard
          key={tool.id}
          tool={tool}
          onClick={onToolClick}
          onDelete={onToolDelete}
          onFork={onToolFork}
          isDeleting={deletingToolId === tool.id}
        />
      ))}
    </div>
  );
};
