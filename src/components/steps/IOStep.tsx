import { useState, useEffect } from "react";
import {
  ArrowUpLef<PERSON>,
  ArrowDownRight,
  Plus,
  Settings,
  Loader2,
  Check,
  X,
  Save,
  Edit,
  FileText,
  MessageSquare,
  Code,
  Bar<PERSON>hart,
  File<PERSON>son,
  Text,
} from "lucide-react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useCreateInput, useUpdateInput } from "@/hooks/useInputs";
import { useOutputs } from "@/hooks/useOutputs";
import { useWorkflow } from "@/contexts/WorkflowContext";
import { toast } from "@/hooks/use-toast";

interface ConfigurableInputComponent {
  id: string;
  configType: string; // FileInput, TextInput, URLInput, etc.
  name: string;
  description: string;
  config: Record<string, any>;
  savedInputId?: string; // ID of the created input component
  isSaving?: boolean;
  isConfigured?: boolean;
  isModified?: boolean; // Track if saved component has been modified
}

interface IOStepProps {
  data: any;
  onUpdate: (data: any) => void;
  onNext?: () => void;
  onPrev?: () => void;
  isValid?: boolean;
}

export const IOStep = ({ data, onUpdate, isValid }: IOStepProps) => {
  // Initialize with one configurable input by default
  const [configurableInputs, setConfigurableInputs] = useState<
    ConfigurableInputComponent[]
  >(
    data?.configurableInputs || [
      {
        id: "1",
        configType: "",
        name: "",
        description: "",
        config: {},
        isConfigured: false,
      },
    ],
  );
  const [selectedOutputId, setSelectedOutputId] = useState<string | null>(
    data?.selectedOutputIds?.[0] || data?.selectedOutputId || null,
  );

  // Get static configs from WorkflowContext (fetched once at startup)
  const { inputConfigs, inputConfigsLoading, inputConfigsError } =
    useWorkflow();

  // Fetch existing outputs (dynamic user-created data)
  const {
    data: outputsData,
    isLoading: outputsLoading,
    error: outputsError,
  } = useOutputs({
    is_active: true,
    limit: 100,
  });

  // Mutation for creating input components
  const createInputMutation = useCreateInput();
  const updateInputMutation = useUpdateInput();

  const outputs = outputsData?.items || [];

  // Update local state when data prop changes (for edit mode)
  useEffect(() => {
    if (data?.configurableInputs) {
      console.log(
        "IOStep: Updating configurableInputs from data prop:",
        data.configurableInputs,
      );
      setConfigurableInputs(data.configurableInputs);
    }
  }, [data?.configurableInputs]);

  useEffect(() => {
    if (data?.selectedOutputId !== undefined) {
      console.log(
        "IOStep: Updating selectedOutputId from data prop:",
        data.selectedOutputId,
      );
      setSelectedOutputId(data.selectedOutputId);
    } else if (data?.selectedOutputIds?.[0]) {
      console.log(
        "IOStep: Updating selectedOutputId from selectedOutputIds:",
        data.selectedOutputIds[0],
      );
      setSelectedOutputId(data.selectedOutputIds[0]);
    }
  }, [data?.selectedOutputId, data?.selectedOutputIds]);

  const handleConfigurableInputsUpdate = (
    updatedInputs: ConfigurableInputComponent[],
  ) => {
    setConfigurableInputs(updatedInputs);

    // Extract the saved input IDs for validation and payload
    const savedInputIds = updatedInputs
      .filter((input) => input.savedInputId)
      .map((input) => input.savedInputId);

    onUpdate?.({
      ...data,
      configurableInputs: updatedInputs,
      selectedInputIds: savedInputIds,
      team_input_ids: savedInputIds, // for backend payload
    });
  };

  const addConfigurableInput = () => {
    const newInput: ConfigurableInputComponent = {
      id: Date.now().toString(),
      configType: "",
      name: "",
      description: "",
      config: {},
      isConfigured: false,
    };
    handleConfigurableInputsUpdate([...configurableInputs, newInput]);
  };

  const removeConfigurableInput = (id: string) => {
    handleConfigurableInputsUpdate(
      configurableInputs.filter((input) => input.id !== id),
    );
  };

  const updateConfigurableInput = (id: string, field: string, value: any) => {
    const updatedInputs = configurableInputs.map((input) => {
      if (input.id === id) {
        const updated = { ...input, [field]: value };

        // If changing config type, reset config and load default config
        if (field === "configType" && value) {
          const selectedConfig = inputConfigs.find(
            (config) => config.label === value,
          );
          if (selectedConfig) {
            updated.config = { ...selectedConfig.config };
            updated.name = selectedConfig.label;
            updated.description = selectedConfig.description;
          }
        }

        // Mark as modified if this is a saved component being edited
        if (input.savedInputId && !input.isModified) {
          updated.isModified = true;
        }

        return updated;
      }
      return input;
    });
    handleConfigurableInputsUpdate(updatedInputs);
  };

  const updateInputConfig = (
    inputId: string,
    configKey: string,
    configValue: any,
  ) => {
    const updatedInputs = configurableInputs.map((input) => {
      if (input.id === inputId) {
        const updated = {
          ...input,
          config: {
            ...input.config,
            [configKey]: configValue,
          },
        };

        // Mark as modified if this is a saved component being edited
        if (input.savedInputId && !input.isModified) {
          updated.isModified = true;
        }

        return updated;
      }
      return input;
    });
    handleConfigurableInputsUpdate(updatedInputs);
  };

  const saveInputComponent = async (
    configurableInput: ConfigurableInputComponent,
  ) => {
    if (!configurableInput.configType || !configurableInput.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Please select an input type and provide a name.",
        variant: "destructive",
      });
      return;
    }

    // Set saving state
    const updatedInputs = configurableInputs.map((input) =>
      input.id === configurableInput.id ? { ...input, isSaving: true } : input,
    );
    setConfigurableInputs(updatedInputs);

    try {
      const selectedConfig = inputConfigs.find(
        (config) => config.label === configurableInput.configType,
      );
      if (!selectedConfig) {
        throw new Error("Selected input configuration not found");
      }

      const inputData = {
        component: {
          provider: selectedConfig.provider,
          component_type: selectedConfig.component_type,
          version: selectedConfig.version,
          component_version: selectedConfig.component_version,
          description:
            configurableInput.description || selectedConfig.description,
          label: configurableInput.name,
          config: configurableInput.config,
        },
      };

      let result;
      if (configurableInput.savedInputId) {
        // Update existing input component
        result = await updateInputMutation.mutateAsync({
          id: configurableInput.savedInputId,
          data: inputData,
        });
      } else {
        // Create new input component
        result = await createInputMutation.mutateAsync(inputData);
      }

      // Update the configurable input with the result
      const finalUpdatedInputs = configurableInputs.map((input) => {
        if (input.id === configurableInput.id) {
          return {
            ...input,
            savedInputId: result.id || configurableInput.savedInputId,
            isSaving: false,
            isConfigured: true,
            isModified: false, // Reset modified state after saving
          };
        }
        return input;
      });

      handleConfigurableInputsUpdate(finalUpdatedInputs);

      toast({
        title: "Success",
        description: `Input component "${configurableInput.name}" ${configurableInput.savedInputId ? "updated" : "created"} successfully.`,
      });
    } catch (error: any) {
      console.error("Failed to save input component:", error);

      // Reset saving state
      const resetInputs = configurableInputs.map((input) =>
        input.id === configurableInput.id
          ? { ...input, isSaving: false }
          : input,
      );
      setConfigurableInputs(resetInputs);

      toast({
        title: "Error",
        description:
          error?.message ||
          `Failed to ${configurableInput.savedInputId ? "update" : "create"} input component. Please try again.`,
        variant: "destructive",
      });
    }
  };

  const handleOutputSelection = (outputId: string) => {
    const newSelectedId = selectedOutputId === outputId ? null : outputId;
    setSelectedOutputId(newSelectedId);

    onUpdate?.({
      ...data,
      selectedOutputId: newSelectedId,
      selectedOutputIds: newSelectedId ? [newSelectedId] : [], // for backward compatibility
      team_output_ids: newSelectedId ? [newSelectedId] : [], // for backend payload
    });
  };

  const renderInputConfigFields = (
    configurableInput: ConfigurableInputComponent,
  ) => {
    if (!configurableInput.configType || !configurableInput.config) return null;

    const configEntries = Object.entries(configurableInput.config);

    return (
      <div className="grid grid-cols-2 gap-3">
        {configEntries.map(([key, value]) => (
          <div key={key}>
            <Label className="text-xs text-gray-500 mb-1 block capitalize">
              {key.replace(/_/g, " ")}
            </Label>
            {typeof value === "boolean" ? (
              <div className="flex items-center space-x-2">
                <Switch
                  checked={value}
                  onCheckedChange={(checked) =>
                    updateInputConfig(configurableInput.id, key, checked)
                  }
                />
                <span className="text-sm text-gray-600">
                  {value ? "Yes" : "No"}
                </span>
              </div>
            ) : typeof value === "object" && value !== null ? (
              <Textarea
                value={JSON.stringify(value, null, 2)}
                onChange={(e) => {
                  try {
                    const parsedValue = JSON.parse(e.target.value);
                    updateInputConfig(configurableInput.id, key, parsedValue);
                  } catch {
                    // Invalid JSON, keep as string for now
                  }
                }}
                className="text-xs font-mono"
                rows={3}
              />
            ) : (
              <Input
                type={typeof value === "number" ? "number" : "text"}
                value={value?.toString() || ""}
                onChange={(e) => {
                  const newValue =
                    typeof value === "number"
                      ? parseInt(e.target.value) || 0
                      : e.target.value;
                  updateInputConfig(configurableInput.id, key, newValue);
                }}
                className="text-sm"
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  const isLoading = inputConfigsLoading || outputsLoading;
  const hasError = inputConfigsError || outputsError;

  if (isLoading) {
    return (
      <div className="space-y-8 max-w-4xl">
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            Input & Output Configuration
          </h3>
          <p className="text-gray-600 text-lg leading-relaxed">
            Configure how your workflow receives input data and formats its
            output responses.
          </p>
        </div>
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-teal-600" />
          <span className="ml-2 text-slate-600">
            Loading available configurations...
          </span>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="space-y-8 max-w-4xl">
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            Input & Output Configuration
          </h3>
          <p className="text-gray-600 text-lg leading-relaxed">
            Configure how your workflow receives input data and formats its
            output responses.
          </p>
        </div>
        <div className="text-center py-20">
          <div className="text-red-600 text-xl font-semibold mb-3">
            Error loading configurations
          </div>
          <p className="text-slate-400 mb-8 text-base">
            {(inputConfigsError as any)?.message ||
              (outputsError as any)?.message ||
              "Something went wrong"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 max-w-4xl">
      <div className="space-y-3">
        <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
          Input & Output Configuration
        </h3>
        <p className="text-gray-600 text-lg leading-relaxed">
          Configure custom input components for your team. Each input component
          can be configured with specific settings and will be created just for
          this team.
        </p>
      </div>

      {/* Inputs Section */}
      <Card className="border border-gray-100 bg-white shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-base">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border border-gray-200">
              <ArrowDownRight className="h-4 w-4 text-gray-700" />
            </div>
            Input Components
            <Badge
              variant="outline"
              className="ml-2 bg-gray-100 text-gray-600 font-normal"
            >
              {configurableInputs.filter((input) => input.savedInputId).length}{" "}
              saved
            </Badge>
          </CardTitle>
          <Button onClick={addConfigurableInput} size="sm" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Input
          </Button>
        </CardHeader>
        <CardContent>
          {inputConfigs.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">
                No input configurations available.
              </p>
              <p className="text-sm text-gray-400">
                Contact your administrator to set up input configurations.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {configurableInputs.map((configurableInput) => (
                <div
                  key={configurableInput.id}
                  className="border rounded-lg p-4 transition-all hover:shadow-md bg-white"
                >
                  <div className="space-y-4">
                    {/* Header with modification indicator */}
                    {configurableInput.isModified && (
                      <div className="flex items-center gap-2 mb-2">
                        <Badge
                          variant="outline"
                          className="border-orange-300 text-orange-600 bg-orange-50"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Modified
                        </Badge>
                        <span className="text-xs text-gray-500">
                          Changes pending save
                        </span>
                      </div>
                    )}

                    {/* Basic Configuration */}
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label className="text-xs text-gray-500 mb-2 block">
                          Input Type
                        </Label>
                        <Select
                          value={configurableInput.configType}
                          onValueChange={(value) =>
                            updateConfigurableInput(
                              configurableInput.id,
                              "configType",
                              value,
                            )
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select input type" />
                          </SelectTrigger>
                          <SelectContent>
                            {inputConfigs.map((config: any) => (
                              <SelectItem
                                key={config.label}
                                value={config.label}
                              >
                                {config.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-xs text-gray-500 mb-2 block">
                          Name
                        </Label>
                        <Input
                          value={configurableInput.name}
                          onChange={(e) =>
                            updateConfigurableInput(
                              configurableInput.id,
                              "name",
                              e.target.value,
                            )
                          }
                          placeholder="Input component name"
                        />
                      </div>

                      <div className="flex items-end justify-between gap-2">
                        {configurableInput.savedInputId &&
                        !configurableInput.isModified ? (
                          <Badge variant="default" className="bg-green-600">
                            <Check className="h-3 w-3 mr-1" />
                            Saved
                          </Badge>
                        ) : (
                          <Button
                            onClick={() =>
                              saveInputComponent(configurableInput)
                            }
                            disabled={
                              !configurableInput.configType ||
                              !configurableInput.name.trim() ||
                              configurableInput.isSaving
                            }
                            size="sm"
                            variant={
                              configurableInput.isModified
                                ? "secondary"
                                : "default"
                            }
                          >
                            {configurableInput.isSaving ? (
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            ) : configurableInput.savedInputId ? (
                              <Edit className="h-4 w-4 mr-2" />
                            ) : (
                              <Save className="h-4 w-4 mr-2" />
                            )}
                            {configurableInput.savedInputId ? "Update" : "Save"}
                          </Button>
                        )}

                        {configurableInputs.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              removeConfigurableInput(configurableInput.id)
                            }
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Description */}
                    {configurableInput.configType && (
                      <div>
                        <Label className="text-xs text-gray-500 mb-2 block">
                          Description
                        </Label>
                        <Textarea
                          value={configurableInput.description}
                          onChange={(e) =>
                            updateConfigurableInput(
                              configurableInput.id,
                              "description",
                              e.target.value,
                            )
                          }
                          placeholder="Describe what this input component will be used for"
                          rows={2}
                        />
                      </div>
                    )}

                    {/* Configuration Fields */}
                    {configurableInput.configType && (
                      <div>
                        <Label className="text-sm font-medium text-gray-700 mb-3 block">
                          Configuration
                        </Label>
                        {renderInputConfigFields(configurableInput)}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Outputs Section */}
      <Card className="border border-gray-100 bg-white shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-base">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border border-gray-200">
              <ArrowUpLeft className="h-4 w-4 text-gray-700" />
            </div>
            Output Components
            <Badge
              variant="outline"
              className="ml-2 bg-gray-100 text-gray-600 font-normal"
            >
              {selectedOutputId ? "1 selected" : "none selected"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {outputs.length === 0 ? (
            <div className="text-center py-4">
              <p className="text-gray-500 mb-2">
                No output components available.
              </p>
              <p className="text-sm text-gray-400">
                Contact your administrator to set up output components.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-4">
              {outputs.map((output: any) => {
                // Map output types to icons based on component label
                const getIcon = () => {
                  const label = output.component.label.toLowerCase();
                  if (label.includes("json"))
                    return <FileJson className="h-4 w-4" />;
                  if (label.includes("markdown"))
                    return <FileText className="h-4 w-4" />;
                  if (label.includes("text"))
                    return <Text className="h-4 w-4" />;
                  if (label.includes("chart"))
                    return <BarChart className="h-4 w-4" />;
                  if (label.includes("code"))
                    return <Code className="h-4 w-4" />;
                  return <FileText className="h-4 w-4" />; // Default to FileText for all others
                };

                return (
                  <div
                    key={output.id}
                    className={`border rounded-lg p-3 transition-all cursor-pointer flex items-center bg-white h-12 ${
                      selectedOutputId === output.id
                        ? "border-gray-800 shadow-sm"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleOutputSelection(output.id)}
                  >
                    <div className="flex-shrink-0 mr-3 text-gray-600">
                      {getIcon()}
                    </div>
                    <h4
                      className={`font-medium text-sm ${
                        selectedOutputId === output.id
                          ? "text-gray-900"
                          : "text-gray-700"
                      }`}
                    >
                      {output.component.label}
                    </h4>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Best Practices */}
      <Card className="border border-amber-200 bg-amber-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-amber-600 text-xs font-bold">!</span>
            </div>
            <div className="space-y-1">
              <h4 className="text-sm font-medium text-amber-800">
                I/O Configuration Approach
              </h4>
              <ul className="text-xs text-amber-700 space-y-1">
                <li>
                  • Configure and save input components specific to this team
                </li>
                <li>
                  • Each saved input component becomes available for this team
                  only
                </li>
                <li>
                  • You can customize configuration values for each input type
                </li>
                <li>
                  • Saved input components can be edited and updated as needed
                </li>
                <li>
                  • Output components are selected from existing available
                  options
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Status */}
      {!isValid && (
        <Card className="border border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-red-600 text-xs font-bold">!</span>
              </div>
              <div>
                <h4 className="text-sm font-medium text-red-800">
                  Configuration Required
                </h4>
                <p className="text-xs text-red-700">
                  Please configure and save at least one input component and
                  select one output component to proceed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
