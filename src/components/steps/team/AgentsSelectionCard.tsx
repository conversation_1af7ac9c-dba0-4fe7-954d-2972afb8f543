import { useState, useMemo } from "react";
import { <PERSON><PERSON>, Loader2 } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useTeams } from "@/hooks/useTeams";

interface AgentsSelectionCardProps {
  selectedTeams?: string[];
  onUpdate?: (selectedTeams: string[]) => void;
}

export const AgentsSelectionCard = ({
  selectedTeams = [],
  onUpdate,
}: AgentsSelectionCardProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  // Query parameters for fetching teams
  const queryParams = useMemo(
    () => ({
      name_like: searchTerm || undefined,
      is_active: true,
      limit: 100,
    }),
    [searchTerm],
  );

  // Fetch teams from backend
  const { data: teamsResponse, isLoading, error } = useTeams(queryParams);
  const teams = (teamsResponse as any)?.items || [];

  const toggleTeam = (teamId: string) => {
    const updatedSelection = selectedTeams.includes(teamId)
      ? selectedTeams.filter((id) => id !== teamId)
      : [...selectedTeams, teamId];

    onUpdate?.(updatedSelection);
  };

  if (isLoading) {
    return (
      <Card className="border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
              <Bot className="h-4 w-4 text-teal-800" />
            </div>
            Sub Agents (Teams)
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-teal-800" />
          <span className="ml-2 text-gray-600">Loading teams...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
              <Bot className="h-4 w-4 text-teal-800" />
            </div>
            Sub Agents (Teams)
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <p className="text-red-600">
            Error loading teams: {(error as any)?.message}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-gray-200 shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-lg">
          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
            <Bot className="h-4 w-4 text-teal-800" />
          </div>
          Sub Agents (Teams)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Label className="text-sm font-medium text-gray-700">
          Select teams to include in your workflow
        </Label>

        {/* Search input */}
        <Input
          placeholder="Search teams..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="h-10 border-gray-300 focus:border-teal-800 focus:ring-teal-800"
        />

        <div className="space-y-3 max-h-64 overflow-y-auto">
          {teams.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No teams found. {searchTerm && "Try adjusting your search terms."}
            </div>
          ) : (
            teams.map((team) => (
              <div
                key={team.id}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-md bg-white ${
                  selectedTeams.includes(team.id)
                    ? "border-gray-400 shadow-md"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => toggleTeam(team.id)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg flex-shrink-0">
                    <Bot className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900">
                        {team.name}
                      </h4>
                      {selectedTeams.includes(team.id) && (
                        <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
                      {team.description}
                    </p>
                    <div className="mt-2 flex items-center gap-2 flex-wrap">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {team.teamType}
                      </span>
                      <span className="text-xs text-gray-500">
                        {team.participantsCount} participants
                      </span>
                      <span className="text-xs text-gray-500">
                        {team.maxTurns} max turns
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {selectedTeams.length > 0 && (
          <div className="mt-4 p-3 bg-teal-50 rounded-lg">
            <p className="text-sm text-teal-800">
              {selectedTeams.length} team{selectedTeams.length > 1 ? "s" : ""}{" "}
              selected
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
