import { <PERSON><PERSON>, <PERSON><PERSON>, FileText, Image } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

const availableTools = [
  { id: "web-search", name: "Web Search", icon: Zap },
  { id: "calculator", name: "Calculator", icon: Wrench },
  { id: "file-reader", name: "File Reader", icon: FileText },
  { id: "image-analysis", name: "Image Analysis", icon: Image },
];

interface ToolsSelectionCardProps {
  selectedTools?: string[];
  onUpdate?: (data: { selectedTools: string[] }) => void;
}

export const ToolsSelectionCard = ({
  selectedTools,
  onUpdate,
}: ToolsSelectionCardProps) => {
  const handleToggleTool = (toolId: string) => {
    const tools = selectedTools || [];
    const newTools = tools.includes(toolId)
      ? tools.filter((id) => id !== toolId)
      : [...tools, toolId];
    onUpdate?.({ selectedTools: newTools });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-3">
          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Wrench className="h-4 w-4 text-purple-600" />
          </div>
          Tools
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          {availableTools.map((tool) => {
            const IconComponent = tool.icon;
            return (
              <div
                key={tool.id}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-md bg-white ${
                  selectedTools?.includes(tool.id)
                    ? "border-gray-400 shadow-md"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => handleToggleTool(tool.id)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg flex-shrink-0">
                    <IconComponent className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900">
                        {tool.name}
                      </h4>
                      {selectedTools?.includes(tool.id) && (
                        <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                      )}
                    </div>
                    <div className="mt-2 flex items-center gap-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Tool
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
