import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Bot } from "lucide-react";

interface AgentBasicsCardProps {
  data?: {
    label?: string;
    description?: string;
  };
  onUpdate?: (data: { label: string; description: string }) => void;
}

export const AgentBasicsCard = ({ data, onUpdate }: AgentBasicsCardProps) => {
  const [label, setLabel] = useState(data?.label || "");
  const [description, setDescription] = useState(data?.description || "");

  const handleLabelChange = (value: string) => {
    setLabel(value);
    onUpdate?.({ label: value, description });
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
    onUpdate?.({ label, description: value });
  };

  return (
    <Card className="border-2 border-teal-100 bg-gradient-to-br from-teal-50 to-indigo-50">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-3">
          <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
            <Bot className="h-4 w-4 text-teal-600" />
          </div>
          Agent Details
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label
            htmlFor="agent-label"
            className="text-sm font-medium text-gray-700"
          >
            Agent Label *
          </Label>
          <Input
            id="agent-label"
            placeholder="e.g., Customer Support Agent, Content Writer..."
            value={label}
            onChange={(e) => handleLabelChange(e.target.value)}
            className="h-11 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
          />
          <p className="text-xs text-gray-500">
            This is the display name for your agent in the workflow.
          </p>
        </div>
        <div className="space-y-2">
          <Label
            htmlFor="agent-description"
            className="text-sm font-medium text-gray-700"
          >
            Description
          </Label>
          <Textarea
            id="agent-description"
            placeholder="Describe what this agent does and how it helps users..."
            value={description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            className="min-h-[100px] resize-none border-gray-300 focus:border-teal-500 focus:ring-teal-500"
          />
        </div>
      </CardContent>
    </Card>
  );
};
