import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Code, FileText, Play } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";

interface JSONEditorProps {
  data: any;
  title: string;
  onUpdate?: (data: any) => void;
  readOnly?: boolean;
}

// Helper to replace provider value with its last word
function getDisplayJSON(data: any) {
  if (!data) return "";
  try {
    const cloned = JSON.parse(JSON.stringify(data));
    if (typeof cloned.provider === "string") {
      const parts = cloned.provider.split(".");
      cloned.provider = parts[parts.length - 1];
    }
    return JSON.stringify(cloned, null, 2);
  } catch {
    return JSON.stringify(data, null, 2);
  }
}

export const JSONEditor = ({
  data,
  title,
  onUpdate,
  readOnly = true,
}: JSONEditorProps) => {
  const [jsonValue, setJsonValue] = useState(() => getDisplayJSON(data));
  const [currentView, setCurrentView] = useState<"form" | "json">("form");
  const [error, setError] = useState<string | null>(null);

  const handleJSONChange = (value: string) => {
    setJsonValue(value);
    setError(null);

    if (!readOnly && onUpdate) {
      try {
        const parsed = JSON.parse(value);
        onUpdate(parsed);
      } catch (e) {
        setError("Invalid JSON format");
      }
    }
  };

  const handleTest = () => {
    console.log("Testing configuration:", data);
    // Add test logic here
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">{title}</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleTest}
              className="h-8 px-3 text-xs"
            >
              <Play className="h-3 w-3 mr-1" />
              Test
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs
          value={currentView}
          onValueChange={(value) => setCurrentView(value as "form" | "json")}
        >
          <div className="px-6 border-b">
            <TabsList className="h-8">
              <TabsTrigger value="form" className="text-xs h-6 px-3">
                <FileText className="h-3 w-3 mr-1" />
                Form Editor
              </TabsTrigger>
              <TabsTrigger value="json" className="text-xs h-6 px-3">
                <Code className="h-3 w-3 mr-1" />
                JSON Editor
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="form" className="p-6 m-0">
            <div className="text-sm text-gray-600">
              Form editor interface would go here. For now, showing JSON view.
            </div>
            <div className="mt-4 p-4 bg-gray-50 rounded-lg text-sm font-mono text-gray-700 max-h-80 overflow-y-auto">
              <pre>{jsonValue}</pre>
            </div>
          </TabsContent>

          <TabsContent value="json" className="p-3 m-0">
            <div
              className="mt-2 p-2 bg-gray-50 rounded-lg text-sm font-mono text-gray-700 overflow-y-auto relative"
              style={{ maxHeight: "none" }} // Remove max-h-60, let content grow
            >
              <Textarea
                value={jsonValue}
                onChange={(e) => handleJSONChange(e.target.value)}
                readOnly={readOnly}
                rows={Math.max(1, jsonValue.split("\n").length)}
                className="font-mono text-sm border-0 rounded-none bg-transparent text-gray-700 resize-none focus:ring-0 focus:border-0"
                style={{
                  backgroundColor: "transparent",
                  color: "#333",
                  lineHeight: "1.4",
                  height: "auto", // Let it auto-size
                  minHeight: 0, // Remove min height
                  maxHeight: "none",
                }}
              />
              {error && (
                <div className="absolute bottom-2 left-2 text-red-400 text-xs bg-red-900/20 px-2 py-1 rounded">
                  {error}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
