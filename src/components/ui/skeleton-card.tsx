import { Card, CardContent } from "@/components/ui/card";

export const SkeletonCard = () => {
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-slate-200 hover:border-teal-300 bg-white">
      <CardContent className="p-6">
        {/* Header with title and status */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            {/* Title skeleton */}
            <div className="h-6 bg-gray-200 rounded animate-pulse mb-2 w-3/4"></div>
            {/* Description skeleton */}
            <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
          </div>
          <div className="ml-4">
            {/* Status badge skeleton */}
            <div className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Team type and stats */}
        <div className="flex items-center gap-4 mb-4">
          {/* Team type skeleton */}
          <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
          {/* Participants skeleton */}
          <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
          {/* Max turns skeleton */}
          <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
        </div>

        {/* Last modified */}
        <div className="mb-6">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            {/* Start Chat button skeleton */}
            <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="flex gap-2">
            {/* Action buttons skeleton */}
            <div className="h-9 w-9 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-9 w-9 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-9 w-9 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-9 w-9 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const SkeletonGrid = ({ count = 9 }: { count?: number }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>
  );
};
