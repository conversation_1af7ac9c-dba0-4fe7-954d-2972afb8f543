import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-gray-50 text-gray-700 border-gray-200",
        secondary: "bg-gray-50 text-gray-700 border-gray-200",
        destructive: "bg-gray-50 text-gray-700 border-gray-200",
        outline: "bg-gray-50 text-gray-700 border-gray-200",
        active: "bg-green-50 text-green-700 border-green-200",
        test: "bg-yellow-50 text-yellow-700 border-yellow-200",
      },
      size: {
        default: "text-xs",
        sm: "text-xs",
        lg: "text-sm",
        xl: "text-base",
        xxl: "text-lg",
        xxxl: "text-xl",
      },
      radius: {
        default: "rounded-full",
        sm: "rounded-md",
        lg: "rounded-lg",
        xl: "rounded-xl",
        xxl: "rounded-2xl",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, radius, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size, radius }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
