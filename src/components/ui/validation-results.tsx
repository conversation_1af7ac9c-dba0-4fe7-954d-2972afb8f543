import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle, XCircle, Info } from "lucide-react";
import { ValidationResponse } from "@/types/validation";

interface ValidationResultsProps {
  validation: ValidationResponse | null;
  isValidating?: boolean;
  className?: string;
}

export const ValidationResults: React.FC<ValidationResultsProps> = ({
  validation,
  isValidating = false,
  className = "",
}) => {
  if (isValidating) {
    return (
      <Alert className={className}>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Validating component configuration...
        </AlertDescription>
      </Alert>
    );
  }

  if (!validation) {
    return null;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Overall status */}
      <div className="flex items-center gap-2">
        {validation.is_valid ? (
          <Badge variant="default" className="bg-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Valid Configuration
          </Badge>
        ) : (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Invalid Configuration
          </Badge>
        )}
      </div>

      {/* Errors */}
      {validation.errors.length > 0 && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Validation Errors:</div>
              {validation.errors.map((error, index) => (
                <div key={index} className="text-sm">
                  <strong>{error.field}:</strong> {error.error}
                  {error.suggestion && (
                    <div className="text-xs text-muted-foreground mt-1">
                      💡 {error.suggestion}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Warnings */}
      {validation.warnings.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Warnings:</div>
              {validation.warnings.map((warning, index) => (
                <div key={index} className="text-sm">
                  <strong>{warning.field}:</strong> {warning.error}
                  {warning.suggestion && (
                    <div className="text-xs text-muted-foreground mt-1">
                      💡 {warning.suggestion}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
