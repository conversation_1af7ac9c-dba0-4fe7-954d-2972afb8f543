import { Label } from "@/components/ui/label";
import FileUploadBox from "../file-upload";

type Props = {
  data: InputData & { component: FileInput };
  updateInput: (input: InputData) => void;
};

export default function FileInputComponent({ data, updateInput }: Props) {
  const onRemove = () =>
    updateInput({
      ...data,
      component: {
        ...data.component,
        config: {},
      } as FileInput,
    });
  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files.item(0);
    if (!f) return;

    const fileData = await new Promise<{
      name: string;
      content: string;
      type: string;
    }>((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        const base64Content = reader.result as string;
        const base64Data = base64Content.split(",")[1] || base64Content;
        resolve({ name: f.name, content: base64Data, type: f.type });
      };
      reader.onerror = reject;
      reader.readAsDataURL(f);
    });

    updateInput({
      ...data,
      component: {
        ...data.component,
        config: {
          name: fileData.name,
          content: fileData.content,
          encoding: "utf-8",
          file_type: fileData.type,
        },
      } as FileInput,
    });
  };

  return (
    <div key={data.id} className="pt-8 flex flex-col gap-2">
      <h4 className="text-md leading-4 text-black font-semibold">
        {data.component.label}
      </h4>
      <Label className="sr-only" htmlFor={data.id}>
        {data.component.label}
      </Label>
      <FileUploadBox
        name={data.component.label.replace(" ", "-")}
        file={null}
        id={data.id}
        fileName={data.component.config.name}
        accept={data.component.config.file_type}
        onFileChange={onFileChange}
        onRemove={onRemove}
      />
    </div>
  );
}
