import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type Props = {
  data: InputData & { component: URLInput };
  updateInput: (input: InputData) => void;
};

export default function URLInputComponent({ data, updateInput }: Props) {
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateInput({
      ...data,
      component: {
        ...data.component,
        config: {
          url: e.target.value,
        },
      } as URLInput,
    });
  };

  return (
    <div key={data.id} className="pt-8 flex flex-col gap-2">
      <h4 className="text-md leading-4 text-black font-semibold">
        {data.component.label}
      </h4>
      <Label className="sr-only" htmlFor={data.id}>
        {data.component.label}
      </Label>
      <Input
        onChange={onChange}
        defaultValue={data.component.config.url}
        className=""
        type="text"
        name={data.component.label.replace(" ", "-")}
      />
    </div>
  );
}
