import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type Props = {
  data: InputData & { component: TextInput };
  updateInput: (input: InputData) => void;
};

export default function TextInputComponent({ data, updateInput }: Props) {
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateInput({
      ...data,
      component: {
        ...data.component,
        config: {
          content: e.target.value,
          encoding: "utf-8",
        },
      },
    });
  };

  return (
    <div key={data.id} className="pt-8 flex flex-col gap-2">
      <h4 className="text-md leading-4 text-black font-semibold">
        {data.component.label}
      </h4>
      <Label className="sr-only" htmlFor={data.id}>
        {data.component.label}
      </Label>
      <Input
        onChange={onChange}
        defaultValue={data.component.config.content}
        className=""
        type="text"
        name={data.component.label.replace(" ", "-")}
      />
    </div>
  );
}
