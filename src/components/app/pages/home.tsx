import { Outlet, useNavigate, useParams } from "react-router";
import Header from "@/components/app/header";
import Sidebar from "@/components/app/sidebar";
import { useAppStore } from "@/lib/app-store";
import { useTeam } from "@/hooks/useTeams";
import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { get_client_env } from "@/lib/env";
import { useSession } from "@/hooks/useSession";

export default function HomeRoute() {
  const store = useAppStore();
  const params = useParams();
  const navigate = useNavigate();

  const env = get_client_env();
  const {
    data: sessions,
    isLoading: isSessionsLoading,
    isRefetching,
  } = useSession(params.teamId);

  const { data: team, isLoading: isTeamLoading } = useTeam(params.teamId);

  useEffect(() => {
    if (isTeamLoading) return;
    store.setTeam(team as any);
  }, [isTeamLoading]);

  useEffect(() => {
    if (isSessionsLoading || isRefetching) return;
    store.setApps(sessions.data.items);

    // select first one, if not selected
    if (typeof params.conversationId === "undefined") {
      const id = sessions.data.items.at(0)?.id;
      if (id) navigate(`/workflows/${params.teamId}/chat/sessions/${id}`);
    }
  }, [isSessionsLoading, isRefetching]);

  return (
    <div className="flex flex-col h-screen w-screen">
      <Header />
      <div className="flex justify-between min-h-0 h-full">
        <Sidebar />
        <Outlet />
      </div>
    </div>
  );
}
