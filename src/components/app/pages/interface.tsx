import ChatInterface from "@/components/app/home/<USER>";
import { get_client_env } from "@/lib/env";
import axios from "axios";
import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useAppStore } from "@/lib/app-store";

const getHeaders = () => {
  const token_type = localStorage.getItem("token_type");
  const access_token = localStorage.getItem("access_token");

  return { Authorization: `${token_type} ${access_token}` };
};

export default function Interface() {
  const params = useParams();
  const store = useAppStore();
  const navigate = useNavigate();

  const env = get_client_env();
  const current_session_id = params.conversationId;

  const { data: session_data, isLoading } = useQuery({
    queryKey: ["sessions", current_session_id],
    queryFn: () =>
      axios
        .get<AppData>(
          `${env.backend_url}/private/sessions/${current_session_id}`,
          {
            headers: getHeaders(),
          },
        )
        .then((res) => res.data),
  });

  useEffect(() => {
    if (isLoading) return;

    if (typeof session_data === "undefined") {
      navigate(`/workflows/${params.teamId}/chat/sessions`);
      return;
    }

    store.setSelectedApp(session_data);
  }, [isLoading]);

  // const showInputSheet = store.team && store.state === "success";

  if (isLoading) {
    return <>Loading...</>;
  }

  return (
    store.selectedApp && (
      <div className="relative flex flex-1 min-w-0 min-h-0 overflow-hidden">
        <div className="flex-1 bg-neutral-100 overflow-auto">
          {!isLoading && <ChatInterface />}

          {/* {loaderData.component_type === "FileInput" && (
            <TextGeneration
              onFilesChange={handleFilesChange}
              onGenerate={handleGenerate}
              maxFiles={10}
            />
          )} */}
        </div>
        {/* Inputs sheet
          // {showInputSheet && (
          //   <InputsSheet collapse={collapse} setCollapse={setCollapse} />
          // )} */}
      </div>
    )
  );
}
