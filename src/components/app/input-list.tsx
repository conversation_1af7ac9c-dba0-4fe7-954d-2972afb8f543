import { useAppStore } from "@/lib/app-store";
import * as input_types from "./inputs"
import React from "react";
import FormWrapper from "../FormWrapper";

type HTMLFormProps = React.DetailedHTMLProps<
  React.FormHTMLAttributes<HTMLFormElement>,
  HTMLFormElement
>;

function InputList(
  props: HTMLFormProps,
  ref: React.ForwardedRef<HTMLFormElement>,
) {
  const store = useAppStore();
  const inputs = store.team?.team_inputs;

  const updateInput = (input: TeamData["team_inputs"][number]) => {
    const modifiedInputs = [...store.team.team_inputs];
    const i = store.team.team_inputs.findIndex((di) => di.id === input.id);

    modifiedInputs[i].component = input.component;
    store.setTeam({ ...store.team, team_inputs: [...modifiedInputs] });
  };

  // don't wrap with form if chat input
  const isChatInput =
    store.team?.team_inputs.findIndex(
      (input) => input.component.provider === "autogen_core.io.ChatInput",
    ) != -1;

  const inputMap = {
    FileInput: input_types.FileInput,
    TextInput: input_types.TextInput,
    URLInput: input_types.URLInput,
    ChatInput: () => <></>
  };

  if (typeof inputs === "undefined") return <>Loading...</>;

  return (
    <FormWrapper disabled={isChatInput} {...props} ref={ref}>
      {inputs.map((di) => {
        const component_type = di.component.provider.split(".").at(-1);
        const InputComponent = inputMap[component_type];

        return (
          <InputComponent key={di.id} data={di} updateInput={updateInput} />
        );
      })}
    </FormWrapper>
  );
}

const InputListForwarded = React.forwardRef<HTMLFormElement, HTMLFormProps>(
  InputList,
);
export default InputListForwarded;
