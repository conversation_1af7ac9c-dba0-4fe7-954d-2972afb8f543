import { cn } from "@/lib/utils";
import { Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAppStore } from "@/lib/app-store";
import { NavLink, useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { get_client_env } from "@/lib/env";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

type Props = {
  app: AppData;
};

export default function SidebarItem({ app }: Props) {
  const queryClient = useQueryClient();
  const store = useAppStore();
  const navigate = useNavigate();

  const onSelectConversation = () => store.setSelectedApp(app);
  const isSelected = store.selectedApp?.id === app.id;

  const env = get_client_env();

  const deleteMutation = useMutation({
    mutationFn: () => {
      return axios.delete(`${env.backend_url}/private/sessions/${app.id}`, {
        headers: {
          Authorization: `${localStorage.getItem(
            "token_type",
          )} ${localStorage.getItem("access_token")}`,
        },
      });
    },
  });

  const onDelete = async () => {
    try {
      const i = store.apps.findIndex((v) => v.id === app.id);
      const newApps = [...store.apps.slice(0, i), ...store.apps.slice(i + 1)];
      const newApp = newApps.length > 0 ? newApps.at(0) : null;

      await deleteMutation.mutateAsync();

      store.setApps(newApps);
      store.setSelectedApp(newApp);

      let url = `/workflows/${store.team.id}/chat/sessions`;
      if (newApp) {
        url = `${url}/${newApp.id}`;
      }
      navigate(url);
    } catch (error) {
      console.error("Failed to delete session:", error);
    }
  };

  return (
    <NavLink to={`/workflows/${store.team?.id}/chat/sessions/${app?.id}`}>
      <div
        onClick={onSelectConversation}
        className={cn(
          "relative flex gap-2 items-center justify-between",
          "border border-transparent max-h-12",
          "p-3 rounded-lg transition-colors cursor-pointer text-sm font-medium",
          isSelected
            ? "bg-teal-800/10 text-teal-800 font-semibold"
            : "hover:bg-teal-800/15",
        )}
        title={app.name}
      >
        <div className="flex gap-2 items-center truncate">
          <div className="truncate">{app.name}</div>
        </div>
        {isSelected && (
          <>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  className="cursor-pointer hover:bg-transparent hover:text-red-500/80"
                  variant={"ghost"}
                >
                  <Trash className="min-w-4 min-h-4 max-w-4 max-h-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    this session.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction asChild>
                    <Button
                      disabled={deleteMutation.isPending}
                      onClick={onDelete}
                      className="cursor-pointer bg-red-600 hover:bg-red-600/80"
                      variant="destructive"
                    >
                      Delete
                    </Button>
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </>
        )}
      </div>
    </NavLink>
  );
}
