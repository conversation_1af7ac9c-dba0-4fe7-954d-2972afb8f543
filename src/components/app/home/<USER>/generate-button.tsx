import Sparkle from "@/components/icons/sparkle";
import { Button, type ButtonProps } from "@/components/ui/button";
import { useAppStore } from "@/lib/app-store";
import { cn } from "@/lib/utils";
import { Loader2, MoveUp } from "lucide-react";

type Props = ButtonProps & {
  label: string;
  loadingLabel: string;
  formRef: React.RefObject<HTMLFormElement>;
};

export default function GenerateButton({
  loadingLabel,
  label,
  formRef,
  ...props
}: Props) {
  const store = useAppStore();

  const onButtonClick = () => {
    if (!formRef.current) return;
    formRef.current.requestSubmit();
  };

  const isChatInput =
    store.team?.team_inputs.findIndex(
      (input) => input.component.provider === "autogen_core.io.ChatInput",
    ) != -1;

  return (
    <Button
      type={"button"}
      onClick={onButtonClick}
      className={cn(
        "flex gap-2 items-center bg-teal-800 hover:bg-teal-800/80 cursor-pointer",
        props.className,
      )}
      disabled={props.disabled && store.state === "loading"}
    >
      {store.state === "loading" ? (
        <Loader2 className="animate-spin" />
      ) : (
          isChatInput ? <MoveUp /> : <Sparkle className="h-6 w-6" />
      )}
      {!isChatInput && (
        <p className="pb-0.5">
          {store.state === "loading" ? loadingLabel : label}
        </p>
      )}
    </Button>
  );
}
