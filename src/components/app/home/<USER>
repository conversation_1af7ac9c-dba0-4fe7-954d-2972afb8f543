import React from "react";
import { cn } from "@/lib/utils";
import InputList from "../input-list";
import { useAppStore } from "@/lib/app-store";
import { Button } from "@/components/ui/button";
import SidebarToggle from "@/components/icons/sidebar-toggle";

import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import GenerateButton from "./chat-interface/generate-button";

type HTMLFormProps = React.DetailedHTMLProps<
  React.FormHTMLAttributes<HTMLFormElement>,
  HTMLFormElement
>;

type Props = HTMLFormProps & {
  collapse: boolean;
  setCollapse: React.Dispatch<React.SetStateAction<boolean>>;
};

function InputsSheet(
  { collapse, setCollapse, ...props }: Props,
  formRef: React.ForwardedRef<HTMLFormElement>,
) {
  const store = useAppStore();
  const isEmpty = store.selectedApp.messages.length == 0;

  if (isEmpty) return null;

  const isChatInput =
    store.team?.team_inputs.findIndex(
      (input) => input.component.provider === "autogen_core.io.ChatInput",
    ) != -1;

  return (
    <Sheet open={!collapse} onOpenChange={(v) => setCollapse(!v)}>
      <SheetTrigger
        className="absolute top-0 right-0 h-full bg-white flex justify-center pt-4 items-start w-16"
        asChild
      >
        <div>
          <Button
            type="button"
            title="Modify Inputs"
            onClick={() => setCollapse((c) => !c)}
            variant="ghost"
          >
            <SidebarToggle
              className={cn("min-w-6", collapse ? "rotate-180" : "")}
            />
          </Button>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Modify Inputs</SheetTitle>
          <SheetDescription className="h-full flex flex-col justify-between"></SheetDescription>
        </SheetHeader>
        <InputList {...props} ref={formRef} />

        {!isChatInput && <GenerateButton
          className="w-full mt-4"
          label={"Regenerate"}
          loadingLabel={"Regenerating"}
          // @ts-ignore
          formRef={formRef}
        />}
      </SheetContent>
    </Sheet>
  );
}

const InputsSheetForwarded = React.forwardRef(InputsSheet);
export default InputsSheetForwarded;
