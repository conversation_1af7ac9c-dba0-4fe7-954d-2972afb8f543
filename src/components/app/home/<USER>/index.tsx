import { cn } from "@/lib/utils";
import Chat<PERSON>rea from "./chat-area";
import ChatPrompt from "./chat-prompt";
import { useAppStore } from "@/lib/app-store";
import { useMemo } from "react";

export default function ChatInterface() {
  const store = useAppStore();

  const isChatInput = useMemo(() => {
    return store.team?.team_inputs.findIndex(
      (input) => input.component.provider === "autogen_core.io.ChatInput",
    ) != -1;
  }, [store.team.team_inputs]);

  return (
    <div className={cn("min-h-full flex flex-col", "px-[10%]")}>
      {/* Chat area - conditionally centered or normal */}
      <div
        className={cn(
          "flex flex-col justify-between flex-1",
          store.selectedApp.messages.length === 0 ? "" : "py-4",
        )}
      >
        <ChatArea />

        {/* If chat input is in the inputs, and chat has started, show the prompt */}
        {isChatInput && (
          <div
            className={cn(
              store.selectedApp.messages.length === 0
                ? "w-full px-[10%] mb-12"
                : "sticky bottom-0 pb-4 bg-neutral-100",
            )}
          >
            <ChatPrompt />
          </div>
        )}
      </div>
    </div>
  );
}
