import { MessageBubble, MessageBubbleSkeleton } from "@/components/app/message-bubble";
import Wand from "@/components/icons/wand";
import InputList from "@/components/app/input-list";
import { useEffect, useMemo, useRef, useState } from "react";
import { get_client_env } from "@/lib/env";
import axios from "axios";
import AuthService from "@/services/authService";
import { useAppStore } from "@/lib/app-store";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { v4 } from "uuid";
import { FileText } from "lucide-react";
import Markdown from "../../markdown";
import CopyButton from "../../copy-button";
import { Skeleton } from "@/components/ui/skeleton";
import InputsSheet from "../input-sheet";
import GenerateButton from "./generate-button";

export default function ChatArea() {
  const queryClient = useQueryClient();
  const store = useAppStore();
  const formRef = useRef<HTMLFormElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [collapse, setCollapse] = useState(true);

  const env = get_client_env();
  const messages = store.selectedApp.messages;

  const isEmpty = useMemo(() => messages.length == 0, [messages])
  const isLoading = useMemo(() => messages.length % 2 == 1, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const token_type = localStorage.getItem("token_type");
  const token = localStorage.getItem("access_token");
  const headers = { Authorization: `${token_type} ${token}` };

  const showInputSheet = store.team && messages.length > 0;

  const isChatInput =
    store.team?.team_inputs.findIndex(
      (v) => v.component.provider === "autogen_core.io.ChatInput",
    ) != -1;

  const promptMutation = useMutation({
    mutationKey: ["prompt", store.selectedApp?.id],
    mutationFn: (options: {
      runId: string;
      data: {
        source: string;
        inputs: any;
      };
    }) => {
      return axios
        .post(
          `${env.backend_url}/private/sessions/chat/${options.runId}`,
          {
            inputs: options.data.inputs,
            source: options.data.source,
          },
          { headers },
        )
        .then((res) => res.data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["team"] });

      store.setState("success");
    },
    onError: (error) => {
      store.setState("error");
    },
  });

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const user = await AuthService.getCurrentUser();

    const run_response = await axios
      .post<RunData>(
        `${env.backend_url}/private/sessions/generate-run`,
        {
          session_id: store.selectedApp.id,
          user_id: user.id,
        },
        { headers },
      )
      .then((res) => res.data);

    let teamInputs = store.team.team_inputs.map((i) => {
      console.log("DEBUG: Processing team input:", JSON.stringify(i, null, 2));

      switch (i.component.provider) {
        case "autogen_core.io.FileInput":
          return {
            type: "FileInput",
            file_type: i.component.config.file_type,
            content: i.component.config.content || "",
            label: i.component.label || "File Input",
          };
        case "autogen_core.io.TextInput":
          return {
            type: "TextInput",
            content: i.component.config.content || "",
            label: i.component.label || "Text Input",
          };
        case "autogen_core.io.URLInput":
          return {
            type: "URLInput",
            content: i.component.config.url || "",
            label: i.component.label || "URL Input",
          };
        default:
          console.warn("DEBUG: Unknown input type:", i.component);
          return {
            type: "TextInput",
            content: "unknown",
            label: "Unknown Input",
          };
      }
    });

    promptMutation.mutate(
      {
        runId: run_response.id,
        data: {
          source: "user",
          inputs: teamInputs,
        },
      },
      {
        onSuccess: (response) => {
          // The response contains a 'result' field with the AI's answer
          if (typeof response === "string") {
            store.addMessage({
              id: v4(),
              run_id: run_response.id,
              session_id: store.selectedApp.id,
              config: {
                source: "system",
                message_type: "text",
                content: response
              },
            });
          }
          else if (response.data?.result) {
            // Add the AI's response to the chat
            store.addMessage({
              id: v4(),
              run_id: run_response.id,
              session_id: store.selectedApp.id,
              config: {
                source: "system",
                message_type: "text",
                content: response.data.result
              },
            });
          } else if (response.data) {
            console.warn("No result in response:", response.data);
            store.addMessage({
              id: v4(),
              run_id: run_response.id,
              session_id: store.selectedApp.id,
              config: {
                source: "system",
                message_type: "text",
                content: response.data
              },
            });
          }

          store.setState("success");
        },
        onError: (error) => {
          console.error("DEBUG: Error response:", error);
          store.setState("error");
        },
      },
    );

    store.setRunId(run_response.id);
    store.setState("loading");
  };

  return (
    <>
      {/* When Empty, show only inputs */}
      {isEmpty && (
        <>
          <div className="flex flex-col items-center w-full">
            {/* Main Content Card */}
            <div className="mt-24 pb-4 bg-white rounded-lg w-4/6 max-w-3xl shadow-sm border border-gray-200">
              <div className="flex justify-between items-center border-b border-b-gray-200 p-4">
                <div className="flex gap-1 items-center">
                  <Wand className="h-6 w-6" />
                  <h2 className="text-xl -tracking-[1%] leading-7 font-semibold">
                    Inputs
                  </h2>
                </div>
                {!isChatInput && <GenerateButton
                  loadingLabel={isChatInput ? "Loading" : "Generating"}
                  label="Generate"
                  formRef={formRef}
                />}
              </div>
              {/* Inputs Section */}
              <div className="px-8 pb-2">

              <InputList ref={formRef} onSubmit={onSubmit} />
              </div>
            </div>
          </div>
        </>
      )}

      {/* Chat Messages */}
      {!isEmpty && isChatInput && (
        <>
          <div className="flex-1 h-8/12 flex flex-col gap-3 overflow-y-auto px-4 pb-4">
            {messages.map(({ id, config }) => {
              return <MessageBubble key={id} content={config.content} role={config.source} />;
            })}
            <div ref={messagesEndRef} />

            {isLoading && <MessageBubbleSkeleton role={"system"} />}
          </div>
        </>
      )}

      {/* Output, not chat messages */}
      {!isEmpty && !isChatInput && (
        <div className="w-full flex justify-between">
          <div className="bg-white w-full mr-[6%] min-h-[300px] rounded-lg shadow-sm border border-gray-200">
            <div className="px-4 py-6 border-b border-b-gray-300 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                <h2 className="text-xl -tracking-[1%] leading-7 font-semibold">
                  Output
                </h2>
              </div>
              <div className="flex gap-2 items-center">
                <div className="flex text-current/60 -ml-3 gap-1">
                  <CopyButton data={messages.at(-1)?.config.content as string} />
                  {!isChatInput && <GenerateButton
                    loadingLabel="Regenerating"
                    label={"Regenerate"}
                    formRef={formRef}
                  />}
                </div>
              </div>
            </div>
            {typeof messages.at(-1) === "undefined" ? (
              <Skeleton className="h-20 w-full rounded-lg" />
            ) : (
              <>
                <div className="p-4">
                    <Markdown>{messages.at(-1).config.content as string}</Markdown>
                </div>
              </>
            )}
          </div>
          {/* Inputs sheet */}
          {showInputSheet && (
            <InputsSheet
              ref={formRef}
              collapse={collapse}
              setCollapse={setCollapse}
              onSubmit={onSubmit}
            />
          )}
        </div>
      )}
    </>
  );
}
