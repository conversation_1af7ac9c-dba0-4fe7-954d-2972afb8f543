import { useRef, useState } from "react";
import { Plus, Upload } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import GoogleDrive from "@/components/icons/gdrive";
import axios from "axios";
import { get_client_env } from "@/lib/env";
import { useAppStore } from "@/lib/app-store";
import AuthService from "@/services/authService";
import { useMutation } from "@tanstack/react-query";
import { v4 as uuid4 } from "uuid";
import { Label } from "@/components/ui/label";
import GenerateButton from "./generate-button";

export default function ChatPrompt() {

  const formRef = useRef<HTMLFormElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [hasContent, setHasContent] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);

  const env = get_client_env();
  const store = useAppStore();

  const promptMutation = useMutation({
    mutationKey: ["prompt", store.selectedApp?.id],
    mutationFn: (options: {
      runId: string;
      data: {
        source: string;
        inputs: any;
      };
    }) => {
      const headers = {
        Authorization: `${localStorage.getItem(
          "token_type",
        )} ${localStorage.getItem("access_token")}`,
      };

      return axios.post(
        `${env.backend_url}/private/sessions/chat/${options.runId}`,
        {
          inputs: options.data.inputs,
          source: options.data.source,
        },
        { headers },
      );
    },
  });

  const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto"; // reset height
      textarea.style.height = `${textarea.scrollHeight}px`; // adjust to content height
    }
    setHasContent(e.currentTarget.value.length != 0);
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!textareaRef.current) return;

    const message = textareaRef.current.value.trim();
    if (!message) return; // don't submit empty

    setIsDisabled(true);

    const formData = new FormData(e.currentTarget);
    const env = get_client_env();

    // create a run
    const token_type = localStorage.getItem("token_type");
    const token = localStorage.getItem("access_token");

    const bearer_token = `${token_type} ${token}`;
    const headers = { Authorization: bearer_token };

    const user = await AuthService.getCurrentUser();

    const response = await axios.post<RunData>(
      `${env.backend_url}/private/sessions/generate-run`,
      {
        session_id: store.selectedApp.id,
        user_id: user.id,
      },
      { headers },
    );

    // optimistically update the user input
    store.addMessage({
      id: uuid4(),
      session_id: store.selectedApp.id,
      run_id: response.data.id,
      config: {
        content: [
          {
            type: "TextInput",
            label: "User Input",
            content: formData.get("user-input") as string
          }
        ],
        source: "user",
        message_type: "user_message"
      }
    });

    // Build inputs according to the exact RunChatMessage schema
    // Based on the example: simple {type, content, label} structure

    let teamInputs = store.team.team_inputs.map((i) => {
      console.log("DEBUG: Processing team input:", JSON.stringify(i, null, 2));

      switch (i.component.provider) {
        case "autogen_core.io.FileInput":
          return {
            type: "FileInput",
            file_type: i.component.config.file_type,
            content: i.component.config.content || "",
            label: i.component.label || "File Input",
          };
        case "autogen_core.io.TextInput":
          return {
            type: "TextInput",
            content: i.component.config.content || "",
            label: i.component.label || "Text Input",
          };
        case "autogen_core.io.URLInput":
          return {
            type: "URLInput",
            content: i.component.config.url || "",
            label: i.component.label || "URL Input",
          };
        case "autogen_core.io.ChatInput":
          return {
            type: "TextInput",
            content: message,
            label: "User Input"
          }
        default:
          console.warn("DEBUG: Unknown input type:", i.component);
          return {
            type: "TextInput",
            content: "unknown",
            label: "Unknown Input",
          };
      }
    });

    // Combine all inputs
    teamInputs = teamInputs.filter((i) => i.content.trim() !== "");

    // Create the payload that matches RunChatMessage schema exactly
    const payload = {
      source: "user",
      inputs: teamInputs,
    };

    promptMutation.mutate(
      {
        runId: response.data.id,
        data: payload,
      },
      {
        onSuccess: (response) => {
          // The response contains a 'result' field with the AI's answer
          if (response.data?.result) {
            // Add the AI's response to the chat
            store.addMessage({
              id: uuid4(),
              run_id: response.data.id,
              session_id: store.selectedApp.id,
              config: {
                message_type: "text",
                source: "system",
                content: response.data.result,
              }
            });
          } else if (response.data) {
            console.warn("No result in response:", response.data);
            store.addMessage({
              id: uuid4(),
              run_id: response.data.id,
              session_id: store.selectedApp.id,
              config: {
                message_type: "text",
                source: "system",
                content: response.data,
              }
            });
          }

          setIsDisabled(false);
        },
        onError: (error) => {
          console.error("DEBUG: Error response:", error);
          setIsDisabled(false);
        },
      },
    );

    store.setRunId(response.data.id);
    store.setTask(formData.get("user-input") as string);

    textareaRef.current.value = "";
    textareaRef.current.style.height = "auto";
    setHasContent(false);
  };

  const onKeyDown: React.DOMAttributes<HTMLTextAreaElement>["onKeyDown"] = (
    event,
  ) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      const form = event.currentTarget.form;
      if (form) form.requestSubmit();
    }
  };

  return (
    <form
      ref={formRef}
      onSubmit={onSubmit}
      className="bg-white w-full shadow-lg p-4 rounded-3xl flex flex-col gap-3"
    >
      {/* Textarea */}
      <Label className="sr-only" htmlFor="user-input">User Prompt</Label>
      <Textarea
        autoFocus
        onKeyDown={onKeyDown}
        ref={textareaRef}
        onChange={handleInput}
        className={cn(
          "border-0 ring-0 outline-0 focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-0 focus-within:border-0 focus-within:ring-0 focus-within:outline-0 shadow-none",
          "resize-none rounded-sm min-h-[40px] max-h-[200px] ",
          // !hasContent ? "scrollbar-hidden" : "overflow-y-auto"
        )}
        name="user-input"
        id="user-input"
        placeholder="Ask me anything"
      />

      {/* Buttons */}
      <div className="flex justify-between px-2">
        <Popover>
          <PopoverTrigger
            className={cn(
              "w-8 h-8 border flex justify-center items-center cursor-pointer rounded-full",
            )}
          >
            <Plus />
          </PopoverTrigger>
          <PopoverContent className="flex flex-col gap-1 max-w-fit rounded-2xl">
            <Button
              type="button"
              variant={"ghost"}
              className="cursor-pointer flex justify-start gap-2 items-center"
            >
              <Upload className="h-5 w-5" />
              <span>Upload from computer</span>
            </Button>
            <Button
              type="button"
              variant={"ghost"}
              className="cursor-pointer flex justify-start gap-2 items-center"
            >
              <GoogleDrive className="h-5 w-5" />
              <span>Add from Google Drive</span>
            </Button>
          </PopoverContent>
        </Popover>
        <GenerateButton
          className={cn(
            "rounded-full bg-teal-800 hover:bg-teal-800/80 hover:text-white text-white",
            "cursor-pointer w-8 h-8",
          )}
          disabled={isDisabled || !hasContent}
          formRef={formRef}
          label="Chat"
          loadingLabel="Loading" />
      </div>
    </form>
  );
}
