import React from "react";
import { FileTextIcon, ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import Star from "@/components/icons/star";
import PDF from "@/components/icons/pdf";
import Retry from "@/components/icons/retry";
import Markdown from "@/components/app/markdown";
import CopyButton from "@/components/app/copy-button";
import { useAppStore } from "@/lib/app-store";
import { Skeleton } from "../ui/skeleton";

const getFileIcon = (type: string) => {
  if (type.startsWith("image")) return <ImageIcon />;
  if (type.includes("pdf")) return <PDF />;
  return <FileTextIcon />;
};

export function MessageBubble({
  content,
  role,
  file,
}: {
    content: Message["config"]["content"];
  role: Message["config"]["source"]
  file?: FileType
}) {
  const store = useAppStore();

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / (1024 * 1024)).toFixed(1) + " MB";
  };

  const icon = getFileIcon(file?.mimetype || "");
  const isUser = role === "user";

  const text_content = typeof content === "string" ? content : content.at(0).content;

  if (!store.team) return null;

  return (
    <div
      className={cn(
        "mt-4 rounded-[16px] flex flex-col gap-2",
        isUser && "lg:self-end",
      )}
    >
      {typeof file !== "undefined" && (
        <div
          className={cn(
            "flex items-center self-end justify-start gap-4 bg-white rounded-2xl shadow-sm",
            "min-w-xs p-4 pr-8",
          )}
        >
          <div className="p-2">{icon}</div>
          <div className="flex flex-col gap-2">
            <span className="text-sm leading-4.5 font-semibold truncate flex-1">
              {file.name}
            </span>
            <span className="text-xs leading-4 text-current/40">
              {formatFileSize(file.size)}
            </span>
          </div>
        </div>
      )}
      <div
        className={cn(
          "px-4 py-3 w-full rounded-2xl flex gap-2 shadow-md",
          isUser ? "bg-teal-800 text-white text-left" : "bg-white",
        )}
      >
        <div>
          {!isUser && (
            <div className="bg-teal-800 text-white p-2 rounded-full">
              <Star className="h-4 w-4 fill-white" />
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2 min-w-md max-w-4xl text-wrap">
          {isUser ? (
            <Markdown variant="inverted">{text_content}</Markdown>
          ) : (
            <>
              <h4 className="text-sm font-semibold leading-[130%]">
                {store.team.component.label}
              </h4>
                <Markdown>{text_content}</Markdown>
              <div className="flex text-current/60 -ml-3 gap-1">
                <Button variant={"ghost"} className="cursor-pointer -mr-1">
                  <Retry className="h-3.5 w-3.5" />
                </Button>
                  <CopyButton data={text_content} />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export function MessageBubbleSkeleton({
  role,
}: {
  role: Message["config"]["source"]
}) {
  const store = useAppStore();
  const isUser = role === "user";

  if (!store.team) return null;

  return (
    <div
      className={cn(
        "mt-4 rounded-[16px] flex flex-col gap-2",
        isUser && "lg:self-end",
      )}
    >
      <div
        className={cn(
          "px-4 py-3 w-full rounded-2xl flex gap-2 shadow-md",
          isUser ? "bg-teal-800 text-white text-left" : "bg-white",
        )}
      >
        <div>
          {!isUser && (
            <div className="bg-teal-800 text-white p-2 rounded-full">
              <Star className="h-4 w-4 fill-white" />
            </div>
          )}
        </div>
        <div className="flex flex-col w-full">
          <h4 className="text-sm font-semibold leading-[130%]">
            {store.team.component.label}
          </h4>
          <div className="mt-2 flex flex-col gap-2 max-w-4xl text-wrap">
            <div className="flex w-full justify-center items-center">
              <div className="flex flex-col gap-1 w-full">
                <Skeleton className="h-4 w-full rounded-full" />
                <Skeleton className="h-4 w-full rounded-full" />
                <Skeleton className="h-4 w-full rounded-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
