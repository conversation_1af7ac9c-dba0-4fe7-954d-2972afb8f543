import { useState, useEffect } from "react";
import { Save, User as UserIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { toast } from "@/hooks/use-toast";
import { useCurrentUser, useUpdateMyProfile } from "@/hooks/useUser";
import { UserProfileUpdate } from "@/api/users";
import { User } from "@/types/user";

interface ProfileUpdateFormProps {
  user?: User;
}

export const ProfileUpdateForm = ({ user }: ProfileUpdateFormProps) => {
  const updateProfileMutation = useUpdateMyProfile();

  // Local state for form data
  const [profileData, setProfileData] = useState<UserProfileUpdate>({
    name: "",
    bio: "",
  });

  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form data when user data loads
  useEffect(() => {
    if (user) {
      const initialData = {
        name: user.name || "",
        bio: user.bio || "",
      };
      setProfileData(initialData);
    }
  }, [user]);

  // Check for changes
  useEffect(() => {
    if (user) {
      const hasNameChange = profileData.name !== user.name;
      const hasBioChange = profileData.bio !== (user.bio || "");
      setHasChanges(hasNameChange || hasBioChange);
    }
  }, [profileData, user]);

  // Handle profile form changes
  const handleProfileChange = (
    field: keyof UserProfileUpdate,
    value: string,
  ) => {
    setProfileData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Save profile changes
  const saveProfile = async () => {
    if (!hasChanges) {
      toast({
        title: "No changes",
        description: "No changes to save.",
      });
      return;
    }

    try {
      // Only send fields that have changed
      const updateData: UserProfileUpdate = {};

      if (profileData.name !== user?.name) {
        updateData.name = profileData.name;
      }

      if (profileData.bio !== (user?.bio || "")) {
        updateData.bio = profileData.bio;
      }

      await updateProfileMutation.mutateAsync(updateData);

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserIcon className="h-5 w-5" />
          Profile Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={profileData.name}
              onChange={(e) => handleProfileChange("name", e.target.value)}
              className="mt-1"
              placeholder="Enter your full name"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={user.email || ""}
              readOnly
              className="mt-1 bg-gray-50"
              title="Email cannot be changed"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="role">Role</Label>
          <Input
            id="role"
            value={user.role || ""}
            readOnly
            className="mt-1 bg-gray-50"
            title="Role cannot be changed"
          />
        </div>

        <div>
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            value={profileData.bio}
            onChange={(e) => handleProfileChange("bio", e.target.value)}
            className="mt-1"
            placeholder="Tell us about yourself..."
            rows={4}
          />
        </div>

        {/* User Information Display */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">
            Account Information
          </h4>
          <div className="space-y-1 text-xs text-gray-600">
            <p>
              <span className="font-medium">User ID:</span> {user.id}
            </p>
            <p>
              <span className="font-medium">Organization ID:</span>{" "}
              {user.organization_id}
            </p>
            <p>
              <span className="font-medium">Created:</span>{" "}
              {user.created_at
                ? new Date(user.created_at).toLocaleDateString()
                : "N/A"}
            </p>
            {user.updated_at && (
              <p>
                <span className="font-medium">Last Updated:</span>{" "}
                {new Date(user.updated_at).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={saveProfile}
            disabled={updateProfileMutation.isPending || !hasChanges}
            className="bg-teal-800 hover:bg-teal-700 disabled:bg-gray-400"
          >
            {updateProfileMutation.isPending && (
              <LoadingSpinner size="sm" message="" />
            )}
            <Save className="w-4 h-4 mr-2" />
            {hasChanges ? "Save Changes" : "No Changes to Save"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
