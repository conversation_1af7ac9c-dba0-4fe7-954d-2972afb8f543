import { useState } from "react";
import { <PERSON>, <PERSON>O<PERSON>, Lock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { toast } from "@/hooks/use-toast";
import { useChangeMyPassword } from "@/hooks/useUser";
import { PasswordUpdate } from "@/api/users";

export const PasswordChangeForm = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordData, setPasswordData] = useState<PasswordUpdate>({
    current_password: "",
    new_password: "",
  });

  const changePasswordMutation = useChangeMyPassword();

  const handlePasswordChange = (field: keyof PasswordUpdate, value: string) => {
    setPasswordData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const isFormValid = () => {
    return (
      passwordData.current_password.length >= 6 &&
      passwordData.new_password.length >= 6 &&
      passwordData.current_password !== passwordData.new_password
    );
  };

  const changePassword = async () => {
    if (!isFormValid()) {
      toast({
        title: "Invalid input",
        description:
          "Please ensure both passwords are at least 6 characters and different.",
        variant: "destructive",
      });
      return;
    }

    try {
      await changePasswordMutation.mutateAsync(passwordData);
      toast({
        title: "Password changed",
        description: "Your password has been updated successfully.",
      });

      // Reset form
      setPasswordData({
        current_password: "",
        new_password: "",
      });
      setShowPasswordForm(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to change password.",
        variant: "destructive",
      });
    }
  };

  const cancelPasswordChange = () => {
    setPasswordData({
      current_password: "",
      new_password: "",
    });
    setShowPasswordForm(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5" />
          Security Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!showPasswordForm ? (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Keep your account secure by regularly updating your password.
            </p>
            <Button
              variant="outline"
              onClick={() => setShowPasswordForm(true)}
              className="w-full sm:w-auto"
            >
              Change Password
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <Label htmlFor="current_password">Current Password *</Label>
              <div className="relative mt-1">
                <Input
                  id="current_password"
                  type={showCurrentPassword ? "text" : "password"}
                  value={passwordData.current_password}
                  onChange={(e) =>
                    handlePasswordChange("current_password", e.target.value)
                  }
                  placeholder="Enter your current password"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="new_password">New Password *</Label>
              <div className="relative mt-1">
                <Input
                  id="new_password"
                  type={showNewPassword ? "text" : "password"}
                  value={passwordData.new_password}
                  onChange={(e) =>
                    handlePasswordChange("new_password", e.target.value)
                  }
                  placeholder="Enter your new password"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Password must be at least 6 characters long and different from
                your current password.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 pt-2">
              <Button
                onClick={changePassword}
                disabled={changePasswordMutation.isPending || !isFormValid()}
                className="bg-teal-800 hover:bg-teal-700 disabled:bg-gray-400"
              >
                {changePasswordMutation.isPending && (
                  <LoadingSpinner size="sm" message="" />
                )}
                Update Password
              </Button>
              <Button
                variant="outline"
                onClick={cancelPasswordChange}
                disabled={changePasswordMutation.isPending}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
