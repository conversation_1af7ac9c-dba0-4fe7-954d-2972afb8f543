// Types for component validation that match the backend ValidationService

export interface ValidationError {
  field: string;
  error: string;
  suggestion?: string;
}

export interface ValidationResponse {
  is_valid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface ValidationRequest {
  component: ComponentModel;
}

// Base component model that matches backend ComponentModel from autogen_core
export interface ComponentModel {
  provider: string;
  component_type: string;
  version?: number;
  component_version?: number;
  description?: string;
  label?: string;
  config: Record<string, any>;
}

// Component test types
export interface ComponentTestRequest {
  component: ComponentModel;
  timeout?: number;
}

export interface ComponentTestResult {
  status: boolean;
  message: string;
  logs?: string[];
}
