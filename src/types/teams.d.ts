type ComponentType = "TextInput" | "FileInput" | "URLInput" | "ChatInput";
type InputProviders = `autogen_core.io.${ComponentType}`;

interface BaseInput {
  provider: InputProviders;
  component_type: string;
  version: number;
  component_version: number;
  description: string;
  label: string;
}

interface TextInput extends BaseInput {
  provider: "autogen_core.io.TextInput";
  config: {
    content: string;
    encoding: string;
  };
}

interface URLInput extends BaseInput {
  provider: "autogen_core.io.URLInput";
  config: {
    url: string;
    headers: { [key: string]: string };
    timeout: number;
    verify_ssl: boolean;
  };
}

interface FileInput extends BaseInput {
  provider: "autogen_core.io.FileInput";
  config: {
    content: string;
    name: string;
    encoding: string;
    file_type: string;
  };
}

interface ChatInput extends BaseInput {
  provider: "autogen_core.io.ChatInput";
  config?: {
    placeholder?: string;
    file?: FileInput["config"];
  };
}

type InputData = {
  id: string;
  organization_id: string;
  component: TextInput | URLInput | FileInput | ChatInput;
  is_deleted: boolean;
  created_at: Date;
  updated_at: Date;
};

type ModelData = {
  component_type: "model";
  component_version: number;
  config: {
    api_key: string;
    model: string;
  };
  description: string;
  label: string;
  provider: string;
  version: number;
};

type TeamConfig = {
  allow_repeated_speaker: boolean;
  emit_team_events: boolean;
  max_selector_attempts: number;
  max_turns: number;
  model_client: ModelData;
  model_client_streaming: boolean;
  participants: [];
  selector_prompt: string;
};

type OrganizationData = {
  created_at: Date;
  description: string;
  id: string;
  is_deleted: boolean;
  name: string;
  updated_at: Date;
};

type TeamData = {
  component: {
    component_type: "team";
    component_version: number;
    config: TeamConfig;
    description: string;
    label: string;
    provider: string;
    version: number;
  };
  id: string;
  created_at: Date;
  updated_at: Date;
  is_deleted: boolean;
  model: {
    created_at: Date;
    description: string;
    id: string;
    is_deleted: boolean;
    name: string;
    updated_at: Date;
  };
  model_ids: string;
  organization_id: string;
  organization: OrganizationData;
  team_inputs: InputData[];
};
