// User types matching backend schema and AuthService User interface
export interface User {
  id: string;
  name: string;
  email: string;
  bio?: string;
  role: string;
  organization_id: string;
  created_at: string;
  updated_at?: string;
  is_deleted?: boolean;
}

// For user profile updates - matches backend UserUpdate schema
export interface UserUpdate {
  name?: string;
  bio?: string;
  email?: string;
  password?: string;
  role?: string;
  organization_id?: string;
}

// User list response from backend
export interface UserList {
  items: User[];
  total: number;
  page: number;
  pages: number;
  size: number;
}

// Filters for getting users
export interface UserFilters {
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  name?: string;
  name_like?: string;
  is_active?: boolean;
}
