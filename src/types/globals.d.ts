type Role = "user" | "assistant";

type MessageConfig =
  | {
    source: "system";
    message_type: "text";
    content: string;
  }
  | {
    source: "user";
    message_type: "user_message";
    content: {
      type: string;
      label: string;
      content: string;
    }[]
  }


type Message = {
  id: string;
  run_id: string;
  session_id: string;
  config: MessageConfig;
  created_at: string;
  updated_at: string | null;
  is_deleted: boolean;
};

type FileType = {
  id: string;
  url: string;
  size: number;
  mimetype: string;
  name: string;
};

interface BaseData {
  id: string;
  user_id: string;
  title: string;
  created_at: Date;
  updated_at: Date;
  tags?: string[];
}

interface Conversation extends BaseData {
  messages: Message[];
}

interface TextGeneration extends BaseData {
  files: FileType[];
  status: "uploading" | "pending" | "failed" | "completed";
  content: string;
}

type AppType = "chat" | "generation";
type Data =
  | {
      type: "chat";
      data: Conversation;
    }
  | {
      type: "generation";
      data: TextGeneration;
    };
