
export interface EnvironmentVariable {
    name: string;
    value: string;
    type: "string" | "number" | "boolean" | "secret";
    description?: string;
    required: boolean;
}

export interface UISettings {
    show_llm_call_events: boolean;
    expanded_messages_by_default: boolean;
    show_agent_flow_by_default: boolean;
}

export interface ComponentModel {
    provider: string;
    component_type: string;
    version: number;
    component_version: number;
    description: string;
    label: string;
    config: {
        model: string;
        api_key: string;
        [key: string]: any;
    };
}

export interface SettingsConfig {
    environment: EnvironmentVariable[];
    default_model_client?: ComponentModel;
    ui: UISettings;
}
