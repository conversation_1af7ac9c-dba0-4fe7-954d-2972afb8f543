
export interface Scope {
    scope: string;
    description: string;
    category: string;
}

export interface APIKey {
    id: string;
    name: string;
    description: string;
    key_prefix: string;
    scopes: string[];
    rate_limit_perminute: number;
    rate_limit_perhour: number;
    rate_limit_perday: number;
    is_active: boolean;
    total_requests: number;

    expires_at: string;
    last_used_at: string;
    created_at: string;
    updated_at: string;
}

export interface APIKeyList {
    items: APIKey[];
    total: number;
    page: number;
    pages: number;
    size: number;
}

export interface APIKeyCreate {
    name: string;
    description?: string;
    scopes: string[];
    expires_at?: string;
}

export interface APIKeyUpdate extends APIKeyCreate {
    is_active: boolean;
}

export interface APIKeyResponse {
    api_key: APIKey; 
    key: string
}