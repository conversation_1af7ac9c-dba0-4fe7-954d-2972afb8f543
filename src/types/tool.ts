export interface ToolConfig {
  component_type: "tool";
  component_version: number;
  config: {
    description: string;
    global_imports: string[];
    has_cancellation_support: boolean;
    name: string; // Python identifier (auto-generated from label)
    source_code: string;
  };
  description: string;
  label: string;
  provider: string;
  version: number;
}

export interface Tool {
  id: string;
  component: ToolConfig;
  organization_id: string;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

// Frontend display type (for UI components)
export interface Tool2 {
  id: string;
  name: string; // Display name (from component.label)
  description: string;
  status: "Active" | "Inactive";
  lastModified: string;
  provider: string;
  label: string;
  is_default: boolean; // Track if this is a default system tool
}

export interface ToolCreate {
  component: ToolConfig;
  organization_id?: string;
}

export interface ToolUpdate {
  component?: Partial<ToolConfig>;
}

export interface ToolResponse extends Tool {}

export interface ToolList {
  items: ToolResponse[];
  total: number;
  page: number;
  pages: number;
  size: number;
}

export interface ToolFilters {
  skip?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  name?: string;
  name_like?: string;
  is_active?: boolean;
}

// Tool component for builder service (matches the builder API response format)
export interface ToolComponent {
  provider: string;
  component_type: string;
  version: number;
  component_version: number;
  description: string;
  label: string;
  config: Record<string, any>;
}
