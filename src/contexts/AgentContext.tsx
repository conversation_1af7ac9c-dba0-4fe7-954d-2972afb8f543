import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import { useAgents, useDeleteAgent } from "../hooks/useAgents";
import { useDebounce } from "../hooks/useDebounce";
import { Agent2 } from "../types/agent";
import { AgentFilters } from "../api/agents";
import { useAuth } from "./AuthContext";

interface AgentContextType {
  // Data
  agents: Agent2[];
  totalAgents: number;
  isLoading: boolean;
  error: any;

  // Search and filters
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isSearching: boolean;

  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  isPaginating: boolean;

  // Actions
  deleteAgent: (agentId: string, agentName: string) => Promise<void>;
  refetchAgents: () => void;

  // Cache management
  isCached: boolean;
}

const AgentContext = createContext<AgentContextType | undefined>(undefined);

interface AgentProviderProps {
  children: React.ReactNode;
}

export const AgentProvider: React.FC<AgentProviderProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const itemsPerPage = 9;

  // Build API filters
  const apiFilters: AgentFilters = {
    skip: (currentPage - 1) * itemsPerPage,
    limit: itemsPerPage,
    sort_by: "updated_at",
    sort_order: "desc",
  };

  // Add search filter if present
  if (debouncedSearchTerm.trim()) {
    apiFilters.name_like = debouncedSearchTerm.trim();
  }

  // Fetch agents from backend with conditional fetching based on authentication
  const {
    data: agentsResponse,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useAgents(isAuthenticated ? apiFilters : undefined);

  // Handle unauthorized access by setting empty agents array
  const agents = useMemo(() => {
    if (!isAuthenticated) return [];
    if (
      (error as any)?.response?.status === 401 ||
      (error as any)?.response?.status === 403
    )
      return [];

    return (agentsResponse as any)?.items || [];
  }, [agentsResponse, isAuthenticated, error]);

  const totalAgents = useMemo(() => {
    if (!isAuthenticated) return 0;
    if (
      (error as any)?.response?.status === 401 ||
      (error as any)?.response?.status === 403
    )
      return 0;

    return (agentsResponse as any)?.total || 0;
  }, [agentsResponse, isAuthenticated, error]);

  const totalPages = Math.ceil(totalAgents / itemsPerPage);

  // Track search and pagination loading state
  const [isSearching, setIsSearching] = React.useState(false);
  const [isPaginating, setIsPaginating] = React.useState(false);

  // Show loading when search term changes (debounced search is happening)
  useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, debouncedSearchTerm]);

  // Show loading when search is happening and we're fetching
  useEffect(() => {
    if (isFetching && debouncedSearchTerm.trim() !== "") {
      setIsSearching(true);
    } else if (!isFetching) {
      setIsSearching(false);
    }
  }, [isFetching, debouncedSearchTerm]);

  // Show loading when pagination changes and we're fetching
  useEffect(() => {
    if (isFetching && !isSearching) {
      setIsPaginating(true);
    } else {
      setIsPaginating(false);
    }
  }, [isFetching, isSearching]);

  // Delete agent mutation
  const deleteAgentMutation = useDeleteAgent();

  const deleteAgent = useCallback(
    async (agentId: string, agentName: string) => {
      try {
        await deleteAgentMutation.mutateAsync({ id: agentId });
        // Refetch agents after successful deletion
        refetch();
      } catch (error) {
        console.error("Failed to delete agent:", error);
        throw error;
      }
    },
    [deleteAgentMutation, refetch],
  );

  // Check if data is cached (has been fetched before)
  const isCached = useMemo(() => {
    return !!agentsResponse && !isLoading;
  }, [agentsResponse, isLoading]);

  const value: AgentContextType = useMemo(
    () => ({
      // Data
      agents,
      totalAgents,
      isLoading: isLoading && isAuthenticated,
      error: isAuthenticated ? error : null,

      // Search and filters
      searchTerm,
      setSearchTerm,
      isSearching,

      // Pagination
      currentPage,
      setCurrentPage,
      totalPages,
      isPaginating,

      // Actions
      deleteAgent,
      refetchAgents: refetch,

      // Cache management
      isCached,
    }),
    [
      agents,
      totalAgents,
      isLoading,
      isAuthenticated,
      error,
      searchTerm,
      setSearchTerm,
      isSearching,
      currentPage,
      setCurrentPage,
      totalPages,
      isPaginating,
      deleteAgent,
      refetch,
      isCached,
    ],
  );

  return (
    <AgentContext.Provider value={value}>{children}</AgentContext.Provider>
  );
};

export const useAgent = (): AgentContextType => {
  const context = useContext(AgentContext);
  if (context === undefined) {
    throw new Error("useAgent must be used within an AgentProvider");
  }
  return context;
};
