import React, { createContext, useContext, useState } from "react";
import {
  useTerminations,
  useDeleteTermination,
} from "../hooks/useTerminations";
import { useDebounce } from "../hooks/useDebounce";
import {
  Termination2,
  TerminationConditionFilters,
} from "../types/termination";

interface TerminationsContextType {
  terminations: Termination2[];
  totalTerminations: number;
  isLoading: boolean;
  error: Error | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isSearching: boolean;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  isPaginating: boolean;
  deleteTermination: (terminationId: string) => Promise<void>;
  refetchTerminations: () => void;
  isCached: boolean;
}

const TerminationsContext = createContext<TerminationsContextType | undefined>(
  undefined,
);

interface TerminationsProviderProps {
  children: React.ReactNode;
}

export const TerminationsProvider: React.FC<TerminationsProviderProps> = ({
  children,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const itemsPerPage = 9;
  const apiFilters: TerminationConditionFilters = {
    skip: (currentPage - 1) * itemsPerPage,
    limit: itemsPerPage,
    sort_by: "updated_at",
    sort_order: "desc",
  };
  if (debouncedSearchTerm.trim()) {
    apiFilters.name_like = debouncedSearchTerm.trim();
  }
  const {
    data: terminationsResponse,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useTerminations(apiFilters);
  const terminations = React.useMemo(() => {
    return terminationsResponse?.items || [];
  }, [terminationsResponse]);
  const totalTerminations = React.useMemo(() => {
    return terminationsResponse?.total || 0;
  }, [terminationsResponse]);
  const totalPages = Math.ceil(totalTerminations / itemsPerPage);
  const [isSearching, setIsSearching] = React.useState(false);
  const [isPaginating, setIsPaginating] = React.useState(false);
  React.useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, debouncedSearchTerm]);
  React.useEffect(() => {
    if (isFetching && debouncedSearchTerm.trim() !== "") {
      setIsSearching(true);
    } else if (!isFetching) {
      setIsSearching(false);
    }
  }, [isFetching, debouncedSearchTerm]);
  React.useEffect(() => {
    if (isFetching && !isSearching) {
      setIsPaginating(true);
    } else {
      setIsPaginating(false);
    }
  }, [isFetching, isSearching]);
  const deleteTerminationMutation = useDeleteTermination();
  const deleteTermination = async (terminationId: string) => {
    try {
      await deleteTerminationMutation.mutateAsync({
        id: terminationId,
        permanent: false,
      });
      refetch();
    } catch (error) {
      console.error("Failed to delete termination:", error);
      throw error;
    }
  };
  const isCached = React.useMemo(() => {
    return !!terminationsResponse && !isLoading;
  }, [terminationsResponse, isLoading]);
  const value: TerminationsContextType = {
    terminations,
    totalTerminations,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteTermination,
    refetchTerminations: refetch,
    isCached,
  };
  return (
    <TerminationsContext.Provider value={value}>
      {children}
    </TerminationsContext.Provider>
  );
};

export const useTerminationsContext = (): TerminationsContextType => {
  const context = useContext(TerminationsContext);
  if (context === undefined) {
    throw new Error(
      "useTerminationsContext must be used within a TerminationsProvider",
    );
  }
  return context;
};
