import React, { createContext, useContext, useState } from "react";
import { useTeams, useDeleteTeam, useTeamConfigs } from "../hooks/useTeams";
import { useInputConfigs } from "../hooks/useInputs";
import { useOutputConfigs } from "../hooks/useOutputs";
import { useDebounce } from "../hooks/useDebounce";
import { Team2, TeamFilters, TeamResponse, TeamComponent } from "../types/team";
import { useAuth } from "./AuthContext";
import { formatRelativeTime } from "../lib/datetime";
import { useQueryClient } from "@tanstack/react-query";

interface WorkflowContextType {
  // Data
  teams: Team2[];
  totalTeams: number;
  isLoading: boolean;
  error: any;

  // Team configs (static data)
  teamConfigs: TeamComponent[];
  teamConfigsLoading: boolean;
  teamConfigsError: any;

  // Input configs (static data)
  inputConfigs: any[];
  inputConfigsLoading: boolean;
  inputConfigsError: any;

  // Output configs (static data)
  outputConfigs: any[];
  outputConfigsLoading: boolean;
  outputConfigsError: any;

  // Search and filters
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  isSearching: boolean;

  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  isPaginating: boolean;

  // Actions
  deleteTeam: (teamId: string, teamName: string) => Promise<void>;
  addNewTeam: (teamResponse: TeamResponse) => void;
  forceRefetch: () => Promise<void>;

  // Stats
  activeWorkflow: number;
  totalWorkflow: number;
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(
  undefined,
);

interface WorkflowProviderProps {
  children: React.ReactNode;
}

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({
  children,
}) => {
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);

  // Local state for optimistic updates
  const [localTeams, setLocalTeams] = useState<Team2[]>([]);

  // Debounce search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Items per page (max 9 as requested)
  const itemsPerPage = 9;

  const forceRefetch = async () => {
    setLocalTeams([]);
    await queryClient.refetchQueries({ queryKey: ["teams"], exact: false });
  };

  // Build API filters
  const apiFilters: TeamFilters = {
    skip: (currentPage - 1) * itemsPerPage,
    limit: itemsPerPage,
    sort_by: "updated_at",
    sort_order: "desc",
  };

  // Add search filter if present
  if (debouncedSearchTerm.trim()) {
    apiFilters.name_like = debouncedSearchTerm.trim();
  }

  // Add status filter if not "all"
  if (statusFilter !== "all") {
    apiFilters.is_active = statusFilter === "active";
  }

  // Fetch teams data - only when authenticated
  const {
    data: teamsResponse,
    isLoading,
    error,
    isFetching,
  } = useTeams(isAuthenticated ? apiFilters : undefined);

  // Fetch team configs (static data) - only when authenticated
  const {
    data: teamConfigs = [],
    isLoading: teamConfigsLoading,
    error: teamConfigsError,
  } = useTeamConfigs();

  // Fetch input configs (static data) - only when authenticated
  const {
    data: inputConfigs = [],
    isLoading: inputConfigsLoading,
    error: inputConfigsError,
  } = useInputConfigs();

  // Fetch output configs (static data) - only when authenticated
  const {
    data: outputConfigs = [],
    isLoading: outputConfigsLoading,
    error: outputConfigsError,
  } = useOutputConfigs();

  // Transform TeamResponse to Team2 format (same logic as in useTeams hook)
  const transformTeamToTeam2 = (team: TeamResponse): Team2 => {
    const config = team.component?.config;
    const label = team.component?.label || "Unknown Team";
    const name = label;
    const description = team.component?.description || "No description";
    const dateToFormat = team.updated_at || team.created_at;
    const lastModified = formatRelativeTime(dateToFormat);
    const teamType = team.component?.provider?.split(".").pop() || "Unknown";

    return {
      id: team.id,
      name,
      description,
      teamType,
      status: !team.is_deleted && team.is_deployed ? "Active" : "Inactive",
      lastModified,
      participantsCount: config?.participants?.length || 0,
      maxTurns: config?.max_turns || 0,
    };
  };

  const serverTeams = teamsResponse?.items || [];
  const totalTeams = teamsResponse?.total || 0;
  const activeWorkflow = teamsResponse?.active_workflow || 0;
  const totalWorkflow = teamsResponse?.total_workflow || 0;
  const totalPages = Math.ceil(totalTeams / itemsPerPage);

  // Merge server teams with local teams, prioritizing local teams for optimistic updates
  const teams = React.useMemo(() => {
    if (localTeams.length === 0) {
      return serverTeams;
    }

    // If we have local teams and we're on the first page with no search/filter, merge them
    if (
      currentPage === 1 &&
      !debouncedSearchTerm.trim() &&
      statusFilter === "all"
    ) {
      const localTeamIds = new Set(localTeams.map((t) => t.id));
      const filteredServerTeams = serverTeams.filter(
        (t) => !localTeamIds.has(t.id),
      );

      // Combine local teams (newest first) with server teams, limit to 9
      const combined = [...localTeams, ...filteredServerTeams].slice(
        0,
        itemsPerPage,
      );
      return combined;
    }

    // For other pages or when filtering, just return server teams
    return serverTeams;
  }, [
    serverTeams,
    localTeams,
    currentPage,
    debouncedSearchTerm,
    statusFilter,
    itemsPerPage,
  ]);

  // Track search and pagination loading state
  const [isSearching, setIsSearching] = React.useState(false);
  const [isPaginating, setIsPaginating] = React.useState(false);

  // Show loading when search term changes (debounced search is happening)
  React.useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, debouncedSearchTerm]);

  // Show loading when filter changes and we're fetching
  React.useEffect(() => {
    if (
      isFetching &&
      (debouncedSearchTerm.trim() !== "" || statusFilter !== "all")
    ) {
      setIsSearching(true);
    } else if (!isFetching) {
      setIsSearching(false);
    }
  }, [isFetching, debouncedSearchTerm, statusFilter]);

  // Show loading when pagination changes and we're fetching
  React.useEffect(() => {
    if (isFetching && !isSearching) {
      setIsPaginating(true);
    } else {
      setIsPaginating(false);
    }
  }, [isFetching, isSearching]);

  // Delete team mutation
  const deleteTeamMutation = useDeleteTeam();

  const deleteTeam = async (
    teamId: string,
    teamName: string,
  ): Promise<void> => {
    await deleteTeamMutation.mutateAsync({ id: teamId, permanent: false });
  };

  // Function to add a new team to local state (optimistic update)
  const addNewTeam = (teamResponse: TeamResponse) => {
    const newTeam = transformTeamToTeam2(teamResponse);

    setLocalTeams((prevLocal) => {
      // Add the new team at the beginning
      const updated = [newTeam, ...prevLocal];

      // If we're on the first page with no search/filter, limit to 9 items
      if (
        currentPage === 1 &&
        !debouncedSearchTerm.trim() &&
        statusFilter === "all"
      ) {
        return updated.slice(0, itemsPerPage);
      }

      return updated;
    });
  };

  // Reset page when search/filter changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, statusFilter]);

  // Clear local teams when search/filter changes or when not on first page
  React.useEffect(() => {
    if (
      debouncedSearchTerm.trim() ||
      statusFilter !== "all" ||
      currentPage !== 1
    ) {
      setLocalTeams([]);
    }
  }, [debouncedSearchTerm, statusFilter, currentPage]);

  const value: WorkflowContextType = {
    // Data
    teams,
    totalTeams,
    isLoading: isLoading && isAuthenticated,
    error: isAuthenticated ? error : null,

    // Team configs (static data)
    teamConfigs,
    teamConfigsLoading,
    teamConfigsError,

    // Input configs (static data)
    inputConfigs,
    inputConfigsLoading,
    inputConfigsError,

    // Output configs (static data)
    outputConfigs,
    outputConfigsLoading,
    outputConfigsError,

    // Search and filters
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    isSearching,

    // Pagination
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,

    // Actions
    deleteTeam,
    addNewTeam,
    forceRefetch,

    // Stats
    activeWorkflow,
    totalWorkflow,
  };

  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  );
};

export const useWorkflow = (): WorkflowContextType => {
  const context = useContext(WorkflowContext);
  if (context === undefined) {
    throw new Error("useWorkflow must be used within a WorkflowProvider");
  }
  return context;
};
