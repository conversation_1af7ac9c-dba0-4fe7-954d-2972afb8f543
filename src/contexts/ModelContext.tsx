import React, { createContext, useContext, useState } from "react";
import { useModels, useDeleteModel } from "../hooks/useModels";
import { useDebounce } from "../hooks/useDebounce";
import { Model2, ModelFilters } from "../types/model";
import { useAuth } from "./AuthContext";

interface ModelContextType {
  models: Model2[];
  totalModels: number;
  isLoading: boolean;
  error: Error | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isSearching: boolean;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  totalPages: number;
  isPaginating: boolean;
  deleteModel: (modelId: string) => Promise<void>;
  refetchModels: () => void;
  isCached: boolean;
}

const ModelContext = createContext<ModelContextType | undefined>(undefined);

interface ModelProviderProps {
  children: React.ReactNode;
}

export const ModelProvider: React.FC<ModelProviderProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const itemsPerPage = 9;
  const apiFilters: ModelFilters = {
    skip: (currentPage - 1) * itemsPerPage,
    limit: itemsPerPage,
    sort_by: "updated_at",
    sort_order: "desc",
  };
  if (debouncedSearchTerm.trim()) {
    apiFilters.name_like = debouncedSearchTerm.trim();
  }
  const {
    data: modelsResponse,
    isLoading,
    error,
    isFetching,
    refetch,
  } = useModels(isAuthenticated ? apiFilters : undefined);
  const models = React.useMemo(() => {
    return modelsResponse?.items || [];
  }, [modelsResponse]);
  const totalModels = React.useMemo(() => {
    return modelsResponse?.total || 0;
  }, [modelsResponse]);
  const totalPages = Math.ceil(totalModels / itemsPerPage);
  const [isSearching, setIsSearching] = React.useState(false);
  const [isPaginating, setIsPaginating] = React.useState(false);
  React.useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchTerm, debouncedSearchTerm]);
  React.useEffect(() => {
    if (isFetching && debouncedSearchTerm.trim() !== "") {
      setIsSearching(true);
    } else if (!isFetching) {
      setIsSearching(false);
    }
  }, [isFetching, debouncedSearchTerm]);
  React.useEffect(() => {
    if (isFetching && !isSearching) {
      setIsPaginating(true);
    } else {
      setIsPaginating(false);
    }
  }, [isFetching, isSearching]);
  const deleteModelMutation = useDeleteModel();
  const deleteModel = async (modelId: string) => {
    try {
      await deleteModelMutation.mutateAsync({ id: modelId, permanent: false });
      refetch();
    } catch (error) {
      console.error("Failed to delete model:", error);
      throw error;
    }
  };
  const isCached = React.useMemo(() => {
    return !!modelsResponse && !isLoading;
  }, [modelsResponse, isLoading]);
  const value: ModelContextType = {
    models,
    totalModels,
    isLoading,
    error,
    searchTerm,
    setSearchTerm,
    isSearching,
    currentPage,
    setCurrentPage,
    totalPages,
    isPaginating,
    deleteModel,
    refetchModels: refetch,
    isCached,
  };
  return (
    <ModelContext.Provider value={value}>{children}</ModelContext.Provider>
  );
};

export const useModelContext = (): ModelContextType => {
  const context = useContext(ModelContext);
  if (context === undefined) {
    throw new Error("useModelContext must be used within a ModelProvider");
  }
  return context;
};
