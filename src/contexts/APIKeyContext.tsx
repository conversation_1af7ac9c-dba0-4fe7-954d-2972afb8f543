import React, { createContext, useContext, useState, useCallback } from "react";
import {
  useQuery,
  useMutation,
  useQueryClient,
  keepPreviousData,
} from "@tanstack/react-query";
import { apikeyAPI } from "../api/api-key";
import {
  APIKey,
  APIKeyList,
  APIKeyCreate,
  APIKeyUpdate,
  APIKeyResponse,
  Scope,
} from "../types/api-key";

interface APIKeyFilters {
  skip?: number;
  limit?: number;
  name_like?: string;
  is_active?: boolean;
}

interface APIKeyContextType {
  // State
  apiKeys: APIKey[];
  scopes: Scope[];
  totalKeys: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  filters: APIKeyFilters;

  // Loading states
  isLoadingKeys: boolean;
  isLoadingScopes: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;

  // Error states
  keysError: Error | null;
  scopesError: Error | null;

  // Actions
  refetchKeys: () => void;
  createAPIKey: (data: APIKeyCreate) => Promise<APIKeyResponse>;
  updateAPIKey: (id: string, data: APIKeyUpdate) => Promise<APIKey>;
  deleteAPIKey: (id: string) => Promise<void>;
  setFilters: (filters: Partial<APIKeyFilters>) => void;
  clearFilters: () => void;

  // Utility functions
  getAPIKeyById: (id: string) => APIKey | undefined;
  getScopesByCategory: (category: string) => Scope[];
  getScopeByName: (name: string) => Scope | undefined;
}

const APIKeyContext = createContext<APIKeyContextType | undefined>(undefined);

export const useAPIKeys = (): APIKeyContextType => {
  const context = useContext(APIKeyContext);
  if (context === undefined) {
    throw new Error("useAPIKeys must be used within an APIKeyProvider");
  }
  return context;
};

interface APIKeyProviderProps {
  children: React.ReactNode;
  initialFilters?: APIKeyFilters;
}

export const APIKeyProvider: React.FC<APIKeyProviderProps> = ({
  children,
  initialFilters = { skip: 0, limit: 10 },
}) => {
  const queryClient = useQueryClient();
  const [filters, setFiltersState] = useState<APIKeyFilters>(initialFilters);

  // Query for API keys
  const {
    data: apiKeysData,
    isLoading: isLoadingKeys,
    error: keysError,
    refetch: refetchKeys,
  } = useQuery<APIKeyList>({
    queryKey: ["api-keys", filters],
    queryFn: () => apikeyAPI.getAll(filters),
    placeholderData: keepPreviousData,
  });

  // Query for available scopes - these are constants, so we cache them indefinitely
  const {
    data: scopes = [],
    isLoading: isLoadingScopes,
    error: scopesError,
  } = useQuery<Scope[]>({
    queryKey: ["api-key-scopes"],
    queryFn: () => apikeyAPI.getScopes(),
    staleTime: Infinity, // Never consider data stale since scopes are constants
    gcTime: Infinity, // Never garbage collect since scopes are constants
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch when component mounts if data exists
    refetchOnReconnect: false, // Don't refetch when reconnecting to network
  });

  // Create API key mutation
  const createMutation = useMutation({
    mutationFn: (data: APIKeyCreate) => apikeyAPI.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["api-keys"] });
    },
  });

  // Update API key mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: APIKeyUpdate }) =>
      apikeyAPI.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["api-keys"] });
      queryClient.invalidateQueries({ queryKey: ["api-key", id] });
    },
  });

  // Delete API key mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => apikeyAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["api-keys"] });
    },
  });

  // Action handlers
  const handleCreateAPIKey = useCallback(
    async (data: APIKeyCreate): Promise<APIKeyResponse> => {
      return await createMutation.mutateAsync(data);
    },
    [createMutation]
  );

  const handleUpdateAPIKey = useCallback(
    async (id: string, data: APIKeyUpdate): Promise<APIKey> => {
      return await updateMutation.mutateAsync({ id, data });
    },
    [updateMutation]
  );

  const handleDeleteAPIKey = useCallback(
    async (id: string): Promise<void> => {
      await deleteMutation.mutateAsync(id);
    },
    [deleteMutation]
  );

  const handleSetFilters = useCallback((newFilters: Partial<APIKeyFilters>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  }, []);

  const handleClearFilters = useCallback(() => {
    setFiltersState(initialFilters);
  }, [initialFilters]);

  // Utility functions
  const getAPIKeyById = useCallback(
    (id: string): APIKey | undefined => {
      return apiKeysData?.items.find((key) => key.id === id);
    },
    [apiKeysData]
  );

  const getScopesByCategory = useCallback(
    (category: string): Scope[] => {
      return scopes.filter((scope) => scope.category === category);
    },
    [scopes]
  );

  const getScopeByName = useCallback(
    (name: string): Scope | undefined => {
      return scopes.find((scope) => scope.scope === name);
    },
    [scopes]
  );

  const value: APIKeyContextType = {
    // State
    apiKeys: apiKeysData?.items || [],
    scopes,
    totalKeys: apiKeysData?.total || 0,
    currentPage: apiKeysData?.page || 1,
    totalPages: apiKeysData?.pages || 0,
    pageSize: apiKeysData?.size || 10,
    filters,

    // Loading states
    isLoadingKeys,
    isLoadingScopes,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,

    // Error states
    keysError: keysError as Error | null,
    scopesError: scopesError as Error | null,

    // Actions
    refetchKeys,
    createAPIKey: handleCreateAPIKey,
    updateAPIKey: handleUpdateAPIKey,
    deleteAPIKey: handleDeleteAPIKey,
    setFilters: handleSetFilters,
    clearFilters: handleClearFilters,

    // Utility functions
    getAPIKeyById,
    getScopesByCategory,
    getScopeByName,
  };

  return (
    <APIKeyContext.Provider value={value}>{children}</APIKeyContext.Provider>
  );
};
