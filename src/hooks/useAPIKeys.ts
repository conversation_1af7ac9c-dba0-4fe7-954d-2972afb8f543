import { useQuery, useMutation, useQueryClient, keepPreviousData } from '@tanstack/react-query';
import { apikeyAPI } from '../api/api-key';
import { APIKey, APIKeyList, APIKeyCreate, APIKeyUpdate } from '../types/api-key';

export interface APIKeyFilters {
  skip?: number;
  limit?: number;
  name_like?: string;
  is_active?: boolean;
}

export const useAPIKeys = (params?: APIKeyFilters) => {
  return useQuery<APIKeyList>({
    queryKey: ['api-keys', params],
    queryFn: () => apikeyAPI.getAll(params),
    placeholderData: keepPreviousData,
  });
};

export const useAPIKey = (id: string) => {
  return useQuery<APIKey>({
    queryKey: ['api-key', id],
    queryFn: () => apikeyAPI.getById(id),
    enabled: !!id,
  });
};

export const useCreateAPIKey = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: APIKeyCreate) => apikeyAPI.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys'] });
    },
  });
};

export const useUpdateAPIKey = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: APIKeyUpdate }) => apikeyAPI.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['api-keys'] });
      queryClient.invalidateQueries({ queryKey: ['api-key', id] });
    },
  });
};

export const useDeleteAPIKey = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => apikeyAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys'] });
    },
  });
};

export const useAvailableScopes = () => {
  return useQuery({
    queryKey: ['api-key-scopes'],
    queryFn: () => apikeyAPI.getScopes(),
  });
}; 