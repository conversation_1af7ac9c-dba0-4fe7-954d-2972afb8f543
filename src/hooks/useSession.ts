import { get_client_env } from "@/lib/env";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

const getHeaders = () => {
  const token_type = localStorage.getItem("token_type");
  const access_token = localStorage.getItem("access_token");

  return { Authorization: `${token_type} ${access_token}` };
};

export const useSession = (teamId: string) => {
  const env = get_client_env();

  const query_data = useQuery({
    enabled: !!teamId,
    queryKey: ["sessions"],
    queryFn: () => {
      const query_params = new URLSearchParams({
        team_id: teamId,
        sort_order: "desc",
        sort_by: "updated_at",
      });
      query_params.append("sort_by", "created_at");

      return axios.get<FetchApps>(
        `${env.backend_url}/private/sessions/?${query_params.toString()}`,
        { headers: getHeaders() },
      );
    },
  });

  return { ...query_data, data: query_data.data };
};
