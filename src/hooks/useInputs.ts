import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { inputsApi } from "@/api/inputs";
import { buildersApi } from "@/api/builders";
import { InputCreate, InputUpdate, InputFilters } from "@/types/input";

export const useInputs = (params?: InputFilters) => {
  return useQuery({
    queryKey: ["inputs", params],
    queryFn: () => inputsApi.getAll(params),
  });
};

export const useInput = (id: string) => {
  return useQuery({
    queryKey: ["input", id],
    queryFn: () => inputsApi.getById(id),
    enabled: !!id,
  });
};

// New hook for input configurations from builder service
export const useInputConfigs = () => {
  return useQuery({
    queryKey: ["builders", "input-configs"],
    queryFn: () => buildersApi.getInputConfigs(),
    staleTime: 10 * 60 * 1000, // 10 minutes - configs don't change often
  });
};

export const useCreateInput = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: InputCreate) => inputsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inputs"] });
    },
  });
};

export const useUpdateInput = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: InputUpdate }) =>
      inputsApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["inputs"] });
      queryClient.invalidateQueries({ queryKey: ["input", id] });
    },
  });
};

export const useDeleteInput = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      permanent = false,
    }: {
      id: string;
      permanent?: boolean;
    }) => inputsApi.delete(id, permanent),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inputs"] });
    },
  });
};

// Hook for fetching multiple inputs by IDs
export const useInputsByIds = (ids: string[]) => {
  return useQuery({
    queryKey: ["inputs", "by-ids", ids],
    queryFn: async () => {
      if (!ids || ids.length === 0) return [];
      const promises = ids.map((id) => inputsApi.getById(id));
      const results = await Promise.allSettled(promises);
      return results
        .filter(
          (result): result is PromiseFulfilledResult<any> =>
            result.status === "fulfilled",
        )
        .map((result) => result.value);
    },
    enabled: ids && ids.length > 0,
  });
};
