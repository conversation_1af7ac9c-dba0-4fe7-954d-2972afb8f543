import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { usersApi, UserProfileUpdate, PasswordUpdate } from "../api/users";
import { UserUpdate, UserFilters } from "../types/user";
import { useAuth } from "../contexts/AuthContext";

// Get current user (using auth context data, but with query for consistency)
export const useCurrentUser = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["currentUser"],
    queryFn: usersApi.getCurrentUser,
    enabled: !!user, // Only fetch if user is authenticated
    initialData: user, // Use auth context data as initial data
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Update current user's profile via /users/me
export const useUpdateMyProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UserProfileUpdate) => usersApi.updateMyProfile(data),
    onSuccess: (updatedUser) => {
      // Update the current user query cache
      queryClient.setQueryData(["currentUser"], updatedUser);
      // Also invalidate auth context query to trigger refetch
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });
    },
  });
};

// Change current user's password
export const useChangeMyPassword = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PasswordUpdate) => usersApi.changeMyPassword(data),
    onSuccess: () => {
      // No need to update user cache since password isn't returned
      // Optionally force re-authentication or show success message
    },
  });
};

// Get all users with filtering
export const useUsers = (params?: UserFilters) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () => usersApi.getAll(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get user by ID
export const useUser = (id: string) => {
  return useQuery({
    queryKey: ["user", id],
    queryFn: () => usersApi.getById(id),
    enabled: !!id,
  });
};

// Update user profile by ID (admin function - backend requires user ID)
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UserUpdate }) =>
      usersApi.updateById(id, data),
    onSuccess: (updatedUser, { id }) => {
      // Update the specific user query cache
      queryClient.setQueryData(["user", id], updatedUser);
      // Update users list cache
      queryClient.invalidateQueries({ queryKey: ["users"] });
      // If this is the current user, update the auth context
      queryClient.invalidateQueries({ queryKey: ["currentUser"] });
    },
  });
};

// Delete user
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, permanent }: { id: string; permanent?: boolean }) =>
      usersApi.delete(id, permanent),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

// Restore user
export const useRestoreUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => usersApi.restore(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};
