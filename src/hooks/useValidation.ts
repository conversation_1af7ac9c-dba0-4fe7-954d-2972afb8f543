import { useMutation } from "@tanstack/react-query";
import { validationApi } from "@/api/validation";
import {
  ValidationRequest,
  ComponentTestRequest,
  ComponentModel,
} from "@/types/validation";

export const useValidateComponent = () => {
  return useMutation({
    mutationFn: (component: ComponentModel) => {
      const request: ValidationRequest = { component };
      return validationApi.validate(request);
    },
  });
};

export const useTestComponent = () => {
  return useMutation({
    mutationFn: ({
      component,
      timeout,
    }: {
      component: ComponentModel;
      timeout?: number;
    }) => {
      const request: ComponentTestRequest = { component, timeout };
      return validationApi.test(request);
    },
  });
};
