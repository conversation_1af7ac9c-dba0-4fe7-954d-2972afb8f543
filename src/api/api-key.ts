import { ApiService } from '../lib/api'
import {
  APIKeyList,
  APIKey,
  APIKeyCreate,
  APIKeyUpdate,
  APIKeyResponse,
  Scope
} from '../types/api-key'

interface Filters {
  skip?: number;
  limit?: number;
  name_like?: string;
  is_active?: boolean;
}

const API_KEY_URL = '/private/api-keys'

export const apikeyAPI = {
  getAll: (params?: Filters): Promise<APIKeyList> => {
    const searchParams = new URLSearchParams(params as Record<string, string>);

    const queryString = searchParams.toString();
    const url = `${API_KEY_URL}${queryString ? `?${queryString}` : ''}`;

    return ApiService.get<APIKeyList>(url);
  },

  getById: (id: string): Promise<APIKey> => {
    return ApiService.get<APIKey>(`${API_KEY_URL}/${id}`);
  },

  getScopes: async (): Promise<Scope[]> => {
    const data = await ApiService.get<{scopes: Scope[]}>(`${API_KEY_URL}/scopes/available`).then(res => res);
    return data.scopes
  },

  create: (data: APIKeyCreate): Promise<APIKeyResponse> => {
    return ApiService.post<APIKeyResponse>(API_KEY_URL, data);
  },

  update: (id: string, data: APIKeyUpdate): Promise<APIKey> => {
    return ApiService.put<APIKey>(`${API_KEY_URL}/${id}`, data);
  },

  delete: (id: string) => {
    return ApiService.delete(`${API_KEY_URL}/${id}`);
  },
}