import { ApiService, API_ENDPOINTS } from "@/lib/api";
import {
  ValidationRequest,
  ValidationResponse,
  ComponentTestRequest,
  ComponentTestResult,
} from "@/types/validation";

export const validationApi = {
  // Validate a component configuration
  validate: (request: ValidationRequest): Promise<ValidationResponse> => {
    return ApiService.post<ValidationResponse>(
      `${API_ENDPOINTS.PRIVATE.VALIDATIONS}/`,
      request,
    );
  },

  // Test a component functionality
  test: (request: ComponentTestRequest): Promise<ComponentTestResult> => {
    return ApiService.post<ComponentTestResult>(
      `${API_ENDPOINTS.PRIVATE.VALIDATIONS}/test`,
      request,
    );
  },
};
