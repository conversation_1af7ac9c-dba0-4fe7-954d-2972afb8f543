import { ApiService, API_ENDPOINTS } from "../lib/api";
import { User, UserUpdate, UserList, UserFilters } from "../types/user";

// Profile update for current user (only safe fields)
export interface UserProfileUpdate {
  name?: string;
  bio?: string;
}

// Password update interface
export interface PasswordUpdate {
  current_password: string;
  new_password: string;
}

export const usersApi = {
  // Get current user (already handled by AuthService, but here for consistency)
  getCurrentUser: (): Promise<User> => {
    return ApiService.get<User>(API_ENDPOINTS.PRIVATE.USER_ME);
  },

  // Update current user's profile via /users/me
  updateMyProfile: (data: UserProfileUpdate): Promise<User> => {
    return ApiService.put<User>(`${API_ENDPOINTS.PRIVATE.USER_ME}`, data);
  },

  // Change current user's password
  changeMyPassword: (data: PasswordUpdate): Promise<{ message: string }> => {
    return ApiService.put<{ message: string }>(
      `${API_ENDPOINTS.PRIVATE.USER_ME}/password`,
      data,
    );
  },

  // Get all users with filtering (backend supports this)
  getAll: (params?: UserFilters): Promise<UserList> => {
    const searchParams = new URLSearchParams();

    if (params?.skip !== undefined)
      searchParams.append("skip", params.skip.toString());
    if (params?.limit !== undefined)
      searchParams.append("limit", params.limit.toString());
    if (params?.sort_by) searchParams.append("sort_by", params.sort_by);
    if (params?.sort_order)
      searchParams.append("sort_order", params.sort_order);
    if (params?.name) searchParams.append("name", params.name);
    if (params?.name_like) searchParams.append("name_like", params.name_like);
    if (params?.is_active !== undefined)
      searchParams.append("is_active", params.is_active.toString());

    const queryString = searchParams.toString();
    const url = `${API_ENDPOINTS.PRIVATE.USERS}${queryString ? `?${queryString}` : ""}`;

    return ApiService.get<UserList>(url);
  },

  // Get user by ID (backend supports this)
  getById: (id: string): Promise<User> => {
    return ApiService.get<User>(`${API_ENDPOINTS.PRIVATE.USERS}/${id}`);
  },

  // Update user by ID (admin function - backend supports this)
  updateById: (id: string, data: UserUpdate): Promise<User> => {
    return ApiService.put<User>(`${API_ENDPOINTS.PRIVATE.USERS}/${id}`, data);
  },

  // Delete user (backend supports soft/hard delete)
  delete: (id: string, permanent = false): Promise<{ success: boolean }> => {
    const url = `${API_ENDPOINTS.PRIVATE.USERS}/${id}${permanent ? "?permanent=true" : ""}`;
    return ApiService.delete<{ success: boolean }>(url);
  },

  // Restore soft-deleted user (backend supports this)
  restore: (id: string): Promise<User> => {
    return ApiService.post<User>(
      `${API_ENDPOINTS.PRIVATE.USERS}/${id}/restore`,
    );
  },
};
