import axios from "axios";
import { get_client_env } from "@/lib/env";
import {
    SettingsConfig,
    EnvironmentVariable
} from "@/types/settings";

const getHeaders = () => {
    const token_type = localStorage.getItem("token_type");
    const access_token = localStorage.getItem("access_token");

    return { Authorization: `${token_type} ${access_token}` };
};

const env = get_client_env();
const SETTINGS_URL = `${env.backend_url}/private/settings`;

const api = {
    // Settings API
    getSettings: async (): Promise<SettingsConfig> => {
        const response = await axios.get(SETTINGS_URL, {
            headers: getHeaders(),
        });
        return response.data;
    },

    addSettings: async (data: Partial<EnvironmentVariable>): Promise<SettingsConfig> => {
        const response = await axios.post(`${SETTINGS_URL}/environment`, data, {
            headers: getHeaders(),
        });
        return response.data;
    },

    updateSettings: async (data: Partial<SettingsConfig>): Promise<SettingsConfig> => {
        const response = await axios.put(SETTINGS_URL, data, {
            headers: getHeaders(),
        });
        return response.data;
    },
};

export default api