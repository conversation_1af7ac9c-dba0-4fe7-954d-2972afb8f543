import { create } from "zustand";

type ChatState = "idle" | "loading" | "error" | "success";
interface AppState {
  task: string;
  runId: string | null;
  selectedApp: AppData | null;
  apps: AppData[];
  team: TeamData;
  state: ChatState;

  setTask: (task: string) => void;
  setRunId: (runId: string | null) => void;
  setSelectedApp: (app: AppData | null) => void;
  setApps: (apps: AppData[]) => void;
  setTeam: (team: TeamData) => void;
  addMessage: (msg: Partial<Message>) => void;
  setState: (msg: ChatState) => void;

  get: (id: string) => void;
}

export const useAppStore = create<AppState>()((set, get) => ({
  task: "",
  runId: null,
  selectedApp: null,
  apps: [],
  team: null,
  state: "idle",

  setTask: (task) => set({ task }),
  setRunId: (runId) => set({ runId }),
  setSelectedApp: (app) => set({ selectedApp: app }),
  setApps: (apps) => set({ apps }),
  setTeam: (team) => set({ team }),
  addMessage: (msg) =>
    set({
      selectedApp: {
        ...get().selectedApp,
        // @ts-ignore
        messages: [...get().selectedApp.messages, msg],
      },
    }),
  setState: (msg) => set({ state: msg }),

  get: (id) => {
    const { apps } = get();
    const app = apps.find((app) => app.id === id);
    if (app) {
      set({ selectedApp: app });
    }
  },
}));
