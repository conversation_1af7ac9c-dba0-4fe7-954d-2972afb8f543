import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function toTitleCase(str: string) {
  return str.replace(
    /\w\S*/g,
    (text) => text.charAt(0).toUpperCase() + text.substring(1).toLowerCase()
  );
}

/**
 * Convert a label to a valid Python identifier
 * - Replaces non-alphanumeric characters with underscores
 * - Ensures it doesn't start with a digit
 * - Adds trailing underscore to avoid reserved words
 */
export function makeIdentifier(s: string): string {
  // Replace non-alphanumeric characters with underscores
  let identifier = s.replace(/[^a-zA-Z0-9]/g, "_");

  // Ensure it doesn't start with a digit
  if (/^\d/.test(identifier)) {
    identifier = "_" + identifier;
  }

  // Handle empty or all-invalid input
  if (!identifier || identifier === "_") {
    identifier = "_unnamed";
  }

  // Always add a trailing underscore to ensure it's not a reserved word
  return identifier + "_";
}
