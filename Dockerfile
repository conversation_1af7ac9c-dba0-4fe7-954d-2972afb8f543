# Use Node.js 20 Alpine as base image
FROM node:20-alpine AS base

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies fresh in builder stage
RUN npm ci

# Copy source code
COPY . .

# Accept BUILD_ENV as a build argument (e.g., "production" or "staging")
ARG BUILD_ENV=production

# Copy the corresponding environment file from the host system
# Ensure that when you build the image, the path to configurations/ is correct
COPY /configurations/.env.${BUILD_ENV} .env

# Build the application
RUN npm run build

# Production image, copy all the files and run the app
FROM nginx:alpine AS runner
WORKDIR /usr/share/nginx/html

# Remove default nginx static assets
RUN rm -rf ./*

# Copy built application
COPY --from=builder /app/dist .

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80/ || exit 1

CMD ["nginx", "-g", "daemon off;"]