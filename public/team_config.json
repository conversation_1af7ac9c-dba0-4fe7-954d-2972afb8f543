{"provider": "autogen_agentchat.teams.RoundRobinGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A team that runs a group chat with participants taking turns in a round-robin fashion\n    to publish a message to all.", "label": "RoundRobinGroupChat", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "planner_agent", "model_client": {"provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "GPT-4o Mini Azure OpenAI model client.", "label": "AzureOpenAI GPT-4o-mini", "config": {"model": "gpt-4o-mini", "api_key": "5viPcZ9EJEwVIiTURowm6yMdUdQVq2QBhVGT0WFMlJ0M2D7bXrzYJQQJ99BDACYeBjFXJ3w3AAABACOGLjHz", "azure_endpoint": "https://newaiplatform.openai.azure.com/", "azure_deployment": "aiplatform", "api_version": "2025-01-01-preview"}}, "tools": [], "handoffs": [], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A helpful assistant that can plan trips.", "system_message": "You are a helpful assistant that can suggest a travel plan for a user based on their request. Respond with a single sentence", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "local_agent", "model_client": {"provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "GPT-4o Mini Azure OpenAI model client.", "label": "AzureOpenAI GPT-4o-mini", "config": {"model": "gpt-4o-mini", "api_key": "5viPcZ9EJEwVIiTURowm6yMdUdQVq2QBhVGT0WFMlJ0M2D7bXrzYJQQJ99BDACYeBjFXJ3w3AAABACOGLjHz", "azure_endpoint": "https://newaiplatform.openai.azure.com/", "azure_deployment": "aiplatform", "api_version": "2025-01-01-preview"}}, "tools": [], "handoffs": [], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A local assistant that can suggest local activities or places to visit.", "system_message": "You are a helpful assistant that can suggest authentic and interesting local activities or places to visit for a user and can utilize any context information provided. Respond with a single sentence", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "language_agent", "model_client": {"provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "GPT-4o Mini Azure OpenAI model client.", "label": "AzureOpenAI GPT-4o-mini", "config": {"model": "gpt-4o-mini", "api_key": "5viPcZ9EJEwVIiTURowm6yMdUdQVq2QBhVGT0WFMlJ0M2D7bXrzYJQQJ99BDACYeBjFXJ3w3AAABACOGLjHz", "azure_endpoint": "https://newaiplatform.openai.azure.com/", "azure_deployment": "aiplatform", "api_version": "2025-01-01-preview"}}, "tools": [], "handoffs": [], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A helpful assistant that can provide language tips for a given destination.", "system_message": "You are a helpful assistant that can review travel plans, providing feedback on important/critical tips about how best to address language or communication challenges for the given destination. If the plan already includes language tips, you can mention that the plan is satisfactory, with rationale.Respond with a single sentence", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that provides assistance with tool use.", "label": "AssistantAgent", "config": {"name": "travel_summary_agent", "model_client": {"provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "GPT-4o Mini Azure OpenAI model client.", "label": "AzureOpenAI GPT-4o-mini", "config": {"model": "gpt-4o-mini", "api_key": "5viPcZ9EJEwVIiTURowm6yMdUdQVq2QBhVGT0WFMlJ0M2D7bXrzYJQQJ99BDACYeBjFXJ3w3AAABACOGLjHz", "azure_endpoint": "https://newaiplatform.openai.azure.com/", "azure_deployment": "aiplatform", "api_version": "2025-01-01-preview"}}, "tools": [], "handoffs": [], "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "A helpful assistant that can summarize the travel plan.", "system_message": "You are a helpful assistant that can take in all of the suggestions and advice from the other agents and provide a detailed tfinal travel plan. You must ensure th b at the final plan is integrated and complete. YOUR FINAL RESPONSE MUST BE THE COMPLETE PLAN. When the plan is complete and all perspectives are integrated, you can respond with TERMINATE.Respond with a single sentence", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}"}}, {"provider": "autogen_agentchat.agents.UserProxyAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "An agent that can represent a human user through an input function.", "label": "UserProxyAgent", "config": {"name": "user_proxy", "description": "a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent"}}], "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 10, "include_agent_event": false}}]}}}}