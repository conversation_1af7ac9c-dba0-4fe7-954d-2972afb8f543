version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_BACKEND_URL: ${VITE_BACKEND_URL:-http://localhost:8000}
        VITE_WEBSOCKET_URL: ${VITE_WEBSOCKET_URL:-ws://localhost:8000/ws}
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    volumes:
      # Mount source code for development (optional, remove for production)
      - .:/app
      - /app/node_modules
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy if needed
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
    profiles:
      - proxy
