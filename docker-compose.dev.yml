version: "3.8"

services:
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_BACKEND_URL=${VITE_BACKEND_URL:-http://localhost:8000}
      - VITE_WEBSOCKET_URL=${VITE_WEBSOCKET_URL:-ws://localhost:8000/ws}
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    restart: unless-stopped
